const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function setupStorage() {
  try {
    console.log('🚀 Setting up Supabase Storage...');

    // Try to create the bucket
    const { data, error } = await supabase.storage.createBucket('json-files', {
      public: false,
      allowedMimeTypes: ['application/json'],
      fileSizeLimit: 10485760 // 10MB
    });

    if (error) {
      if (error.message.includes('already exists')) {
        console.log('✅ Storage bucket "json-files" already exists');
      } else {
        console.error('❌ Error creating bucket:', error);
        return;
      }
    } else {
      console.log('✅ Storage bucket "json-files" created successfully');
    }

    // Test upload to verify permissions
    const testContent = JSON.stringify({ test: true });
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('json-files')
      .upload('test.json', testContent, {
        contentType: 'application/json',
        upsert: true
      });

    if (uploadError) {
      console.error('❌ Test upload failed:', uploadError);
    } else {
      console.log('✅ Test upload successful');

      // Clean up test file
      await supabase.storage.from('json-files').remove(['test.json']);
      console.log('✅ Test file cleaned up');
    }

    console.log('🎉 Storage setup complete!');

  } catch (error) {
    console.error('❌ Setup failed:', error);
  }
}

setupStorage();

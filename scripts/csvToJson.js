#!/usr/bin/env node
/**
 * Convert CSV intake data back to JSON format
 * Usage: node csvToJson.js [input.csv] [output.json]
 */

const fs = require('fs');
const path = require('path');

function csvToJson(csvFile, outputFile) {
  console.log('🔄 Converting CSV to JSON...');

  try {
    // Read CSV file
    const csvContent = fs.readFileSync(csvFile, 'utf8');
    const lines = csvContent.trim().split('\n');
    const header = lines[0];

    console.log(`📂 Processing ${lines.length - 1} data rows...`);

    // Parse CSV data
    const collegeMap = new Map();

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i];

      // Simple CSV parsing (handles quoted fields)
      const match = line.match(/^"([^"]*?)","([^"]*?)","([^"]*?)"$/);
      if (!match) {
        console.log(`⚠️  Skipping malformed line ${i}: ${line}`);
        continue;
      }

      const [, collegeName, courseName, date] = match;

      // Validate date
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) {
        console.log(`⚠️  Invalid date: ${date} in line ${i}`);
        continue;
      }

      // Build college structure
      if (!collegeMap.has(collegeName)) {
        collegeMap.set(collegeName, new Map());
      }

      const courseMap = collegeMap.get(collegeName);
      if (!courseMap.has(courseName)) {
        courseMap.set(courseName, new Set());
      }

      courseMap.get(courseName).add(date);
    }

    // Convert to final JSON structure
    const colleges = [];

    for (const [collegeName, courseMap] of collegeMap) {
      const courses = [];

      for (const [courseName, dateSet] of courseMap) {
        const intakes = Array.from(dateSet)
          .sort() // Sort dates chronologically
          .map(date => ({ date }));

        courses.push({
          courseName,
          intakes
        });
      }

      // Sort courses by name
      courses.sort((a, b) => a.courseName.localeCompare(b.courseName));

      colleges.push({
        collegeName,
        courses
      });
    }

    // Sort colleges by name
    colleges.sort((a, b) => a.collegeName.localeCompare(b.collegeName));

    // Write JSON file
    fs.writeFileSync(outputFile, JSON.stringify(colleges, null, 2));

    // Generate summary
    const totalCourses = colleges.reduce((sum, college) => sum + college.courses.length, 0);
    const totalIntakes = colleges.reduce((sum, college) =>
      sum + college.courses.reduce((courseSum, course) => courseSum + course.intakes.length, 0), 0
    );

    console.log('\n📊 CONVERSION SUMMARY:');
    console.log(`   📚 Colleges: ${colleges.length}`);
    console.log(`   🎓 Courses: ${totalCourses}`);
    console.log(`   📅 Total Intakes: ${totalIntakes}`);
    console.log(`\n✅ JSON saved to: ${outputFile}`);

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Command line usage
if (require.main === module) {
  const csvFile = process.argv[2] || '../intake-dates.csv';
  const outputFile = process.argv[3] || '../intake-dates.json';

  if (!fs.existsSync(csvFile)) {
    console.error(`❌ CSV file not found: ${csvFile}`);
    process.exit(1);
  }

  csvToJson(csvFile, outputFile);
}

module.exports = { csvToJson };

const fs = require('fs');
const path = require('path');

/**
 * Clean and format intake dates JSON data
 */
function cleanIntakeData() {
  try {
    console.log('🔧 Starting data cleanup process...\n');

    // Read the backup file
    const backupPath = path.join(__dirname, '../intake-dates-backup.json');
    const rawData = fs.readFileSync(backupPath, 'utf8');

    console.log(`📂 Read ${rawData.length} characters from backup file`);

    // Remove problematic characters and fix formatting
    let cleanedData = rawData
      // Remove trailing commas before closing brackets/braces
      .replace(/,(\s*[\]\}])/g, '$1')
      // Fix irregular spacing and line breaks
      .replace(/\r\n/g, '\n')
      // Remove trailing array syntax issue
      .replace(/\]\s*;\s*$/, ']')
      // Fix opening structure if malformed
      .replace(/^{\s*"collegeName"/, '[\n  {\n    "collegeName"');

    // Try to parse and validate
    let parsedData;
    try {
      parsedData = JSON.parse(cleanedData);
    } catch (parseError) {
      console.error('❌ Initial parse failed, attempting manual fixes...');

      // More aggressive cleaning
      cleanedData = cleanedData
        // Fix missing commas between objects
        .replace(/\}\s*\{/g, '},\n  {')
        // Fix missing commas between array elements
        .replace(/\]\s*\[/g, '],\n    [')
        // Ensure proper array closure
        .replace(/\}\s*\]\s*$/, '}\n]');

      try {
        parsedData = JSON.parse(cleanedData);
      } catch (secondParseError) {
        console.error('❌ Failed to parse data after cleanup attempts');
        console.error('Error:', secondParseError.message);
        return;
      }
    }

    // Validate and clean the parsed data
    const validatedData = validateAndCleanData(parsedData);

    // Write the cleaned data
    const outputPath = path.join(__dirname, '../intake-dates.json');
    fs.writeFileSync(outputPath, JSON.stringify(validatedData, null, 2));

    // Generate report
    generateReport(validatedData);

    console.log('\n✅ Data cleanup completed successfully!');
    console.log(`📁 Clean data written to: ${outputPath}`);

  } catch (error) {
    console.error('❌ Error during cleanup process:', error.message);
  }
}

/**
 * Validate and clean the data structure
 */
function validateAndCleanData(data) {
  console.log('\n🔍 Validating data structure...');

  if (!Array.isArray(data)) {
    console.log('⚠️  Converting single object to array format');
    data = [data];
  }

  const cleanedColleges = [];
  let totalIssuesFixed = 0;

  data.forEach((college, collegeIndex) => {
    if (!college.collegeName) {
      console.log(`⚠️  College at index ${collegeIndex} missing name, skipping`);
      return;
    }

    const cleanedCollege = {
      collegeName: college.collegeName.trim(),
      courses: []
    };

    if (!Array.isArray(college.courses)) {
      console.log(`⚠️  College "${college.collegeName}" has no courses array`);
      return;
    }

    college.courses.forEach((course, courseIndex) => {
      if (!course.courseName) {
        console.log(`⚠️  Course at index ${courseIndex} in "${college.collegeName}" missing name`);
        return;
      }

      const cleanedCourse = {
        courseName: course.courseName.trim(),
        intakes: []
      };

      if (Array.isArray(course.intakes)) {
        const seenDates = new Set();

        course.intakes.forEach((intake, intakeIndex) => {
          if (!intake.date) {
            console.log(`⚠️  Missing date at intake ${intakeIndex} in "${course.courseName}"`);
            totalIssuesFixed++;
            return;
          }

          // Validate date format
          const date = new Date(intake.date);
          if (isNaN(date.getTime())) {
            console.log(`⚠️  Invalid date "${intake.date}" in "${course.courseName}"`);
            totalIssuesFixed++;
            return;
          }

          // Format date consistently (YYYY-MM-DD)
          const formattedDate = date.toISOString().split('T')[0];

          // Check for duplicates
          if (seenDates.has(formattedDate)) {
            console.log(`⚠️  Duplicate date "${formattedDate}" in "${course.courseName}"`);
            totalIssuesFixed++;
            return;
          }

          seenDates.add(formattedDate);
          cleanedCourse.intakes.push({ date: formattedDate });
        });

        // Sort intakes by date
        cleanedCourse.intakes.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
      }

      if (cleanedCourse.intakes.length > 0) {
        cleanedCollege.courses.push(cleanedCourse);
      }
    });

    if (cleanedCollege.courses.length > 0) {
      cleanedColleges.push(cleanedCollege);
    }
  });

  console.log(`✅ Validation complete. Fixed ${totalIssuesFixed} issues.`);
  return cleanedColleges;
}

/**
 * Generate a report about the data
 */
function generateReport(data) {
  console.log('\n📊 DATA REPORT');
  console.log('================');

  const totalColleges = data.length;
  let totalCourses = 0;
  let totalIntakes = 0;
  const allDates = [];

  data.forEach(college => {
    totalCourses += college.courses.length;
    college.courses.forEach(course => {
      totalIntakes += course.intakes.length;
      course.intakes.forEach(intake => {
        allDates.push(new Date(intake.date));
      });
    });
  });

  const oldestDate = new Date(Math.min(...allDates));
  const newestDate = new Date(Math.max(...allDates));

  console.log(`📚 Colleges: ${totalColleges}`);
  console.log(`🎓 Courses: ${totalCourses}`);
  console.log(`📅 Total Intakes: ${totalIntakes}`);
  console.log(`📆 Date Range: ${oldestDate.toISOString().split('T')[0]} to ${newestDate.toISOString().split('T')[0]}`);

  // Detailed breakdown
  console.log('\n📋 College Breakdown:');
  data.forEach(college => {
    const courseCount = college.courses.length;
    const intakeCount = college.courses.reduce((sum, course) => sum + course.intakes.length, 0);
    console.log(`  • ${college.collegeName}: ${courseCount} courses, ${intakeCount} intakes`);
  });

  // Save report
  const reportPath = path.join(__dirname, '../data-report.txt');
  const reportContent = `
Student Deferment Calculator - Data Report
Generated: ${new Date().toISOString()}

SUMMARY:
- Colleges: ${totalColleges}
- Courses: ${totalCourses}
- Total Intakes: ${totalIntakes}
- Date Range: ${oldestDate.toISOString().split('T')[0]} to ${newestDate.toISOString().split('T')[0]}

COLLEGE DETAILS:
${data.map(college => {
  const courseCount = college.courses.length;
  const intakeCount = college.courses.reduce((sum, course) => sum + course.intakes.length, 0);
  return `${college.collegeName}: ${courseCount} courses, ${intakeCount} intakes`;
}).join('\n')}
`;

  fs.writeFileSync(reportPath, reportContent);
  console.log(`📄 Detailed report saved to: ${reportPath}`);
}

// Run the cleanup
if (require.main === module) {
  cleanIntakeData();
}

module.exports = { cleanIntakeData, validateAndCleanData };

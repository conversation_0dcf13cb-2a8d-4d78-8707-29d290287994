#!/usr/bin/env node
/**
 * Sync JSON intake data to Supabase
 * This script imports course and intake data from intake-dates.json to Supabase
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Location mappings (as per design document)
const LOCATION_MAPPINGS = {
  'SYD': 'Sydney',
  'MELB': 'Melbourne',
  'MEL': 'Melbourne',
  'BNE': 'Brisbane',
  'BSB': 'Brisbane',
  'HOB': 'Hobart',
  'HOBA': 'Hobart',
  'PER': 'Perth',
  'ADL': 'Adelaide'
};

// VET code extraction patterns
const VET_CODE_PATTERNS = [
  /([A-Z]{3}\d{5})/,     // Standard: BSB80120
  /([A-Z]{3}\d{4}[A-Z]?)/,  // Variant: CHC30121
  /([A-Z]{2,4}\d{4,5})/     // Flexible: ICT40120
];

// Location extraction patterns
const LOCATION_PATTERNS = [
  /\(([^)]+)\)$/,           // Extract from parentheses at end
  /\(([^)]+)\)\s*-/,        // Extract from parentheses before dash
  /-\s*([A-Z, &]+)$/        // Extract after dash at end
];

class JsonToSupabaseSync {
  constructor() {
    this.stats = {
      providers: { created: 0, existing: 0 },
      faculties: { created: 0, existing: 0 },
      courses: { created: 0, existing: 0, updated: 0 },
      locations: { created: 0, existing: 0 },
      intakes: { created: 0, deleted: 0 }
    };
  }

  async sync() {
    try {
      console.log('🚀 Starting JSON to Supabase sync...\n');

      // Read JSON data
      const jsonPath = path.join(__dirname, '../intake-dates.json');
      const jsonData = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));

      console.log(`📂 Loaded ${jsonData.length} colleges from JSON`);

      // Process each college
      for (const college of jsonData) {
        await this.processCollege(college);
      }

      // Generate report
      this.generateReport();

      console.log('\n✅ Sync completed successfully!');

    } catch (error) {
      console.error('❌ Sync failed:', error.message);
      process.exit(1);
    }
  }

  async processCollege(collegeData) {
    console.log(`\n🏢 Processing college: ${collegeData.collegeName}`);

    // Create or get provider
    const provider = await this.ensureProvider(collegeData.collegeName);

    // Create default faculty for this provider
    const faculty = await this.ensureFaculty(provider.id, collegeData.collegeName);

    // Process each course
    for (const courseData of collegeData.courses) {
      await this.processCourse(courseData, faculty.id, collegeData.collegeName);
    }
  }

  async ensureProvider(providerName) {
    // Check if provider exists
    const { data: existing } = await supabase
      .from('providers')
      .select('*')
      .eq('name', providerName)
      .single();

    if (existing) {
      this.stats.providers.existing++;
      return existing;
    }

    // Create new provider
    const { data: newProvider, error } = await supabase
      .from('providers')
      .insert({ name: providerName })
      .select()
      .single();

    if (error) throw error;

    this.stats.providers.created++;
    console.log(`  ✅ Created provider: ${providerName}`);
    return newProvider;
  }

  async ensureFaculty(providerId, facultyName) {
    // Check if faculty exists
    const { data: existing } = await supabase
      .from('faculties')
      .select('*')
      .eq('provider_id', providerId)
      .eq('name', facultyName)
      .single();

    if (existing) {
      this.stats.faculties.existing++;
      return existing;
    }

    // Create new faculty
    const { data: newFaculty, error } = await supabase
      .from('faculties')
      .insert({
        provider_id: providerId,
        name: facultyName
      })
      .select()
      .single();

    if (error) throw error;

    this.stats.faculties.created++;
    console.log(`  ✅ Created faculty: ${facultyName}`);
    return newFaculty;
  }

  async processCourse(courseData, facultyId, providerName) {
    const courseName = courseData.courseName;
    console.log(`  📚 Processing course: ${courseName}`);

    // Extract VET code and locations from course name
    const vetCode = this.extractVetCode(courseName);
    const locationCodes = this.extractLocationCodes(courseName);
    const cleanCourseName = this.cleanCourseName(courseName);

    // Create or get course
    const course = await this.ensureCourse(facultyId, cleanCourseName, vetCode, providerName);

    // Process locations and intakes
    if (locationCodes.length > 0) {
      // Course has specific locations
      for (const locationCode of locationCodes) {
        const location = await this.ensureLocation(locationCode);
        await this.syncIntakes(course.id, location.id, courseData.intakes);
      }
    } else {
      // Course has no specific location, use a default
      const defaultLocation = await this.ensureLocation('General');
      await this.syncIntakes(course.id, defaultLocation.id, courseData.intakes);
    }
  }

  extractVetCode(courseName) {
    for (const pattern of VET_CODE_PATTERNS) {
      const match = courseName.match(pattern);
      if (match) return match[1];
    }
    return null;
  }

  extractLocationCodes(courseName) {
    const locations = [];

    for (const pattern of LOCATION_PATTERNS) {
      const match = courseName.match(pattern);
      if (match) {
        // Split by common separators and clean up
        const locationStr = match[1];
        const codes = locationStr.split(/[,&]/).map(s => s.trim());

        for (const code of codes) {
          // Map code to full name if possible
          const mappedLocation = LOCATION_MAPPINGS[code.toUpperCase()] || code;
          if (!locations.includes(mappedLocation)) {
            locations.push(mappedLocation);
          }
        }
        break;
      }
    }

    return locations;
  }

  cleanCourseName(courseName) {
    // Remove location codes from course name
    let cleaned = courseName;

    for (const pattern of LOCATION_PATTERNS) {
      cleaned = cleaned.replace(pattern, '').trim();
    }

    return cleaned;
  }

  async ensureCourse(facultyId, courseName, vetCode, providerName) {
    // First check if this exact course already exists for this faculty
    let existing = null;

    if (vetCode) {
      const { data } = await supabase
        .from('courses')
        .select('*')
        .eq('faculty_id', facultyId)
        .eq('vet_code', vetCode)
        .single();
      existing = data;
    }

    if (!existing) {
      const { data } = await supabase
        .from('courses')
        .select('*')
        .eq('faculty_id', facultyId)
        .eq('course_name', courseName)
        .single();
      existing = data;
    }

    if (existing) {
      // Update VET code if we found it and it's missing
      if (vetCode && !existing.vet_code) {
        const { error } = await supabase
          .from('courses')
          .update({ vet_code: vetCode })
          .eq('id', existing.id);

        if (!error) {
          this.stats.courses.updated++;
          console.log(`    ✅ Updated VET code for: ${courseName}`);
        }
      }

      this.stats.courses.existing++;
      return existing;
    }

    // Get CRICOS code from existing course with same VET code, or generate placeholder
    let cricosCode = await this.getCricosCodeForCourse(vetCode, courseName);

    try {
      // Create new course
      const { data: newCourse, error } = await supabase
        .from('courses')
        .insert({
          faculty_id: facultyId,
          course_name: courseName,
          vet_code: vetCode,
          cricos_code: cricosCode
        })
        .select()
        .single();

      if (error) throw error;

      this.stats.courses.created++;
      console.log(`    ✅ Created course: ${courseName}`);
      return newCourse;

    } catch (error) {
      // If we get a duplicate CRICOS code error, it means the same course exists in another provider
      if (error.message.includes('courses_cricos_code_key')) {
        console.log(`    ℹ️  Course with CRICOS ${cricosCode} already exists, creating provider-specific version`);

        // Generate a provider-specific CRICOS code to avoid conflicts
        const providerSpecificCricos = `${cricosCode.replace('P', '')}${providerName.substring(0, 1)}`;

        const { data: newCourse, error: retryError } = await supabase
          .from('courses')
          .insert({
            faculty_id: facultyId,
            course_name: courseName,
            vet_code: vetCode,
            cricos_code: providerSpecificCricos
          })
          .select()
          .single();

        if (retryError) throw retryError;

        this.stats.courses.created++;
        console.log(`    ✅ Created course with provider-specific CRICOS: ${courseName}`);
        return newCourse;
      }

      throw error;
    }
  }

  async ensureLocation(locationName) {
    // Check if location exists
    const { data: existing } = await supabase
      .from('locations')
      .select('*')
      .eq('name', locationName)
      .single();

    if (existing) {
      this.stats.locations.existing++;
      return existing;
    }

    // Create new location
    const { data: newLocation, error } = await supabase
      .from('locations')
      .insert({
        name: locationName,
        code: this.getLocationCode(locationName)
      })
      .select()
      .single();

    if (error) throw error;

    this.stats.locations.created++;
    console.log(`    ✅ Created location: ${locationName}`);
    return newLocation;
  }

  getLocationCode(locationName) {
    // Reverse lookup for location codes
    for (const [code, name] of Object.entries(LOCATION_MAPPINGS)) {
      if (name === locationName) return code;
    }

    // Generate code from name
    return locationName.substring(0, 3).toUpperCase();
  }

  async getCricosCodeForCourse(vetCode, courseName) {
    // First, try to find existing course with same VET code to reuse CRICOS code
    if (vetCode) {
      const { data: existingCourse } = await supabase
        .from('courses')
        .select('cricos_code')
        .eq('vet_code', vetCode)
        .limit(1)
        .single();

      if (existingCourse && existingCourse.cricos_code) {
        console.log(`    ℹ️  Reusing CRICOS code ${existingCourse.cricos_code} for VET code ${vetCode}`);
        return existingCourse.cricos_code;
      }
    }

    // If no existing course found, generate a placeholder CRICOS code
    // Note: In production, these should be replaced with real CRICOS codes
    const placeholderCode = this.generatePlaceholderCricosCode(courseName, vetCode);
    console.log(`    ⚠️  Generated placeholder CRICOS code ${placeholderCode} for ${courseName}`);
    return placeholderCode;
  }

  generatePlaceholderCricosCode(courseName, vetCode) {
    // Generate a placeholder CRICOS code that follows the pattern
    // Real CRICOS codes should be obtained from the government registry

    let baseCode = '';

    if (vetCode) {
      // Use VET code as base if available
      baseCode = vetCode.replace(/[^0-9]/g, ''); // Extract numbers only
    } else {
      // Generate from course name hash
      let hash = 0;
      for (let i = 0; i < courseName.length; i++) {
        const char = courseName.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
      }
      baseCode = Math.abs(hash).toString();
    }

    // Ensure it's 6 digits and add a suffix to make it unique
    const paddedCode = baseCode.padStart(6, '0').substring(0, 6);
    return `${paddedCode}P`; // 'P' for Placeholder
  }

  async syncIntakes(courseId, locationId, intakeData) {
    // Delete existing intakes for this course-location combination
    const { error: deleteError } = await supabase
      .from('intakes')
      .delete()
      .eq('course_id', courseId)
      .eq('location_id', locationId);

    if (deleteError) throw deleteError;

    // Count deleted intakes
    const { count: deletedCount } = await supabase
      .from('intakes')
      .select('*', { count: 'exact', head: true })
      .eq('course_id', courseId)
      .eq('location_id', locationId);

    this.stats.intakes.deleted += deletedCount || 0;

    // Validate and filter intake dates
    const validIntakes = [];
    const invalidIntakes = [];

    for (const intake of intakeData) {
      if (this.isValidDate(intake.date)) {
        validIntakes.push({
          course_id: courseId,
          location_id: locationId,
          intake_date: intake.date
        });
      } else {
        invalidIntakes.push(intake.date);
        console.log(`      ⚠️  Skipping invalid date: ${intake.date}`);
      }
    }

    // Report invalid dates
    if (invalidIntakes.length > 0) {
      console.log(`      ⚠️  Found ${invalidIntakes.length} invalid dates: ${invalidIntakes.join(', ')}`);
    }

    // Insert valid intakes
    if (validIntakes.length > 0) {
      const { error: insertError } = await supabase
        .from('intakes')
        .insert(validIntakes);

      if (insertError) throw insertError;

      this.stats.intakes.created += validIntakes.length;
      console.log(`      ✅ Synced ${validIntakes.length} intake dates`);
    } else {
      console.log(`      ⚠️  No valid intake dates to sync`);
    }
  }

  isValidDate(dateString) {
    // Check for obviously invalid formats
    if (!dateString || typeof dateString !== 'string') {
      return false;
    }

    // Check for invalid day/month combinations like "1900-01-00"
    if (dateString.includes('-00') || dateString.includes('-13') || dateString.includes('-32')) {
      return false;
    }

    // Try to parse the date
    const date = new Date(dateString);

    // Check if the date is valid and matches the input string
    if (isNaN(date.getTime())) {
      return false;
    }

    // Additional validation: ensure the date string matches what we expect
    const isoString = date.toISOString().split('T')[0];
    return isoString === dateString;
  }

  generateReport() {
    console.log('\n📊 SYNC REPORT');
    console.log('================');
    console.log(`🏢 Providers: ${this.stats.providers.created} created, ${this.stats.providers.existing} existing`);
    console.log(`🏫 Faculties: ${this.stats.faculties.created} created, ${this.stats.faculties.existing} existing`);
    console.log(`📚 Courses: ${this.stats.courses.created} created, ${this.stats.courses.existing} existing, ${this.stats.courses.updated} updated`);
    console.log(`📍 Locations: ${this.stats.locations.created} created, ${this.stats.locations.existing} existing`);
    console.log(`📅 Intakes: ${this.stats.intakes.created} created, ${this.stats.intakes.deleted} deleted`);
  }
}

// Run the sync
if (require.main === module) {
  const sync = new JsonToSupabaseSync();
  sync.sync();
}

module.exports = { JsonToSupabaseSync };

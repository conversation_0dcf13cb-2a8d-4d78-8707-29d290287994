#!/usr/bin/env python3
"""
Robust intake dates data parser and cleaner
<PERSON>les malformed JSON and creates a clean, validated output
"""

import json
import re
from datetime import datetime
from typing import List, Dict, Any

def clean_json_string(content: str) -> str:
    """Clean and fix common JSON formatting issues"""
    print("🔧 Cleaning JSON formatting...")

    # Remove problematic characters
    content = re.sub(r'\r\n', '\n', content)
    content = re.sub(r'\r', '\n', content)

    # Remove trailing commas before closing brackets/braces
    content = re.sub(r',(\s*[\]\}])', r'\1', content)

    # Fix malformed structure - remove wrapping object if present
    content = re.sub(r'^\s*{\s*"collegeName"', r'[{"collegeName"', content)
    content = re.sub(r'\]\s*}\s*$', r']', content)
    content = re.sub(r'\]\s*;\s*$', r']', content)

    # Clean up excessive whitespace
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)

    return content.strip()

def extract_colleges_from_text(content: str) -> List[Dict[str, Any]]:
    """Extract college data using regex patterns as fallback"""
    print("🔍 Extracting data using pattern matching...")

    colleges = []

    # Find all college blocks
    college_pattern = r'"collegeName":\s*"([^"]+)"'
    college_matches = re.finditer(college_pattern, content)

    for match in college_matches:
        college_name = match.group(1)
        print(f"  📚 Found college: {college_name}")

        # Find the start of this college's data
        start_pos = match.start()

        # Find courses for this college
        courses = []
        course_pattern = r'"courseName":\s*"([^"]+)"'

        # Look for courses after this college name
        remaining_content = content[start_pos:]
        next_college_match = re.search(r'"collegeName":', remaining_content[100:])

        if next_college_match:
            college_content = remaining_content[:next_college_match.start() + 100]
        else:
            college_content = remaining_content

        course_matches = re.finditer(course_pattern, college_content)

        for course_match in course_matches:
            course_name = course_match.group(1)

            # Find intake dates for this course
            course_start = course_match.start()
            course_content = college_content[course_start:]

            # Find next course or end
            next_course = re.search(r'"courseName":', course_content[100:])
            if next_course:
                course_data = course_content[:next_course.start() + 100]
            else:
                course_data = course_content

            # Extract dates
            date_pattern = r'"date":\s*"([^"]+)"'
            date_matches = re.finditer(date_pattern, course_data)

            intakes = []
            seen_dates = set()

            for date_match in date_matches:
                date_str = date_match.group(1)

                # Validate and format date
                try:
                    if date_str == "1900-01-00":  # Skip invalid placeholder dates
                        continue

                    date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                    formatted_date = date_obj.strftime("%Y-%m-%d")

                    if formatted_date not in seen_dates:
                        intakes.append({"date": formatted_date})
                        seen_dates.add(formatted_date)
                except ValueError:
                    print(f"    ⚠️  Invalid date: {date_str} in {course_name}")
                    continue

            if intakes:
                # Sort intakes by date
                intakes.sort(key=lambda x: x["date"])
                courses.append({
                    "courseName": course_name.strip(),
                    "intakes": intakes
                })
                print(f"    🎓 Course: {course_name} ({len(intakes)} intakes)")

        if courses:
            colleges.append({
                "collegeName": college_name.strip(),
                "courses": courses
            })

    return colleges

def main():
    print("🚀 Starting intake data processing...\n")

    try:
        # Read the backup file
        with open('../intake-dates-backup.json', 'r', encoding='utf-8') as f:
            raw_content = f.read()

        print(f"📂 Read {len(raw_content)} characters from backup file")

        # Try to parse as JSON first
        colleges_data = []

        try:
            cleaned_content = clean_json_string(raw_content)
            colleges_data = json.loads(cleaned_content)
            print("✅ Successfully parsed as JSON")

        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing failed: {e}")
            print("🔄 Falling back to pattern extraction...")
            colleges_data = extract_colleges_from_text(raw_content)

        # Validate and clean the data
        if not isinstance(colleges_data, list):
            print("⚠️  Converting to list format")
            colleges_data = [colleges_data] if colleges_data else []

        # Generate statistics
        total_colleges = len(colleges_data)
        total_courses = sum(len(college.get("courses", [])) for college in colleges_data)
        total_intakes = sum(
            len(course.get("intakes", []))
            for college in colleges_data
            for course in college.get("courses", [])
        )

        print(f"\n📊 DATA SUMMARY:")
        print(f"   📚 Colleges: {total_colleges}")
        print(f"   🎓 Courses: {total_courses}")
        print(f"   📅 Total Intakes: {total_intakes}")

        # Save cleaned data
        output_file = '../intake-dates.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(colleges_data, f, indent=2, ensure_ascii=False)

        print(f"\n✅ Clean data saved to: {output_file}")

        # Generate detailed report
        generate_report(colleges_data)

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def generate_report(colleges_data: List[Dict[str, Any]]):
    """Generate a detailed report about the data"""

    report_lines = [
        "Student Deferment Calculator - Data Report",
        f"Generated: {datetime.now().isoformat()}",
        "",
        "SUMMARY:",
        f"- Colleges: {len(colleges_data)}",
        f"- Total Courses: {sum(len(college.get('courses', [])) for college in colleges_data)}",
        f"- Total Intakes: {sum(len(course.get('intakes', [])) for college in colleges_data for course in college.get('courses', []))}",
        "",
        "COLLEGE BREAKDOWN:",
    ]

    for college in colleges_data:
        college_name = college.get("collegeName", "Unknown")
        courses = college.get("courses", [])
        course_count = len(courses)
        intake_count = sum(len(course.get("intakes", [])) for course in courses)

        report_lines.append(f"• {college_name}: {course_count} courses, {intake_count} intakes")

        # List courses
        for course in courses:
            course_name = course.get("courseName", "Unknown")
            intake_count = len(course.get("intakes", []))
            report_lines.append(f"  - {course_name}: {intake_count} intakes")

    # Save report
    with open('../data-report.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))

    print("📄 Detailed report saved to: data-report.txt")

if __name__ == "__main__":
    main()

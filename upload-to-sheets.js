// Script to prepare data for Google Sheets upload
const fs = require('fs');

console.log('📖 Reading processed intake data...');

// Read the processed data
const processedData = JSON.parse(fs.readFileSync('intake-data-for-sheets.json', 'utf8'));

console.log(`📊 Data contains ${processedData.rows.length} rows`);

// Split into batches of 10 for easier uploading
const batchSize = 10;
const batches = [];

for (let i = 0; i < processedData.rows.length; i += batchSize) {
  const batch = processedData.rows.slice(i, i + batchSize);
  batches.push(batch);
}

console.log(`📦 Created ${batches.length} batches of up to ${batchSize} rows each`);

// Save first batch for testing
fs.writeFileSync('batch-1-intake-data.json', JSON.stringify(batches[0], null, 2));
console.log('💾 First batch saved to batch-1-intake-data.json');

// Show first batch preview
console.log('\n🔍 First batch preview:');
batches[0].forEach((row, index) => {
  console.log(`${index + 1}. ${row[0]} - ${row[1]}`);
});

// College name mapping
const collegeMapping = {
  'AIBT': 'AIBT',
  'AVTA': 'AVT', 
  'AIBT-I': 'AAIBT',
  'Reach College': 'REACH',
  'Brooklyn': 'IBIC', // Assuming Brooklyn maps to IBIC
  'NPA': 'NPA'
};

console.log('\n📝 Applying college name mapping...');
const mappedRows = processedData.rows.map(row => {
  const originalCollege = row[0];
  const mappedCollege = collegeMapping[originalCollege] || originalCollege;
  return [mappedCollege, ...row.slice(1)];
});

console.log('✅ College names mapped successfully');

// Save mapped data
fs.writeFileSync('mapped-intake-data.json', JSON.stringify(mappedRows, null, 2));
console.log('💾 Mapped data saved to mapped-intake-data.json');

// Show mapping results
console.log('\n🔄 College Mapping Results:');
Object.keys(collegeMapping).forEach(original => {
  const mapped = collegeMapping[original];
  const count = processedData.rows.filter(row => row[0] === original).length;
  console.log(`   ${original} → ${mapped} (${count} courses)`);
});

console.log('\n🎯 Ready for Google Sheets upload!');
console.log('   Total rows:', mappedRows.length);
console.log('   Mapped colleges:', Object.values(collegeMapping).join(', '));
console.log('   Range needed: A2:V' + (mappedRows.length + 1));
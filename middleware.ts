import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createServerClient, type CookieOptions } from '@supabase/ssr';

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  // Create a Supabase client using cookies
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://djyzepqwkquomocdgnpd.supabase.co';
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqeXplcHF3a3F1b21vY2RnbnBkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAzMDE5NzMsImV4cCI6MjA2NTg3Nzk3M30.uvxsYk9vFB5eiRpnPBCrj63YmQfK9SZD_CpRvvlmxcs';

  // Skip middleware for login page and static assets
  if (request.nextUrl.pathname.startsWith('/login') ||
      request.nextUrl.pathname.startsWith('/_next') ||
      request.nextUrl.pathname.includes('.')) {
    return response;
  }

  // For all other routes, client-side redirection will handle auth checks
  return response;
}

// Specify which routes this middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.png$).*)',
  ],
};

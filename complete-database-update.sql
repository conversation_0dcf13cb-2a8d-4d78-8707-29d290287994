-- Complete SQL for updating courses database from Google Sheets
-- Generated on 2025-07-18T09:26:43.278Z
-- 
-- INSTRUCTIONS:
-- 1. Run this SQL in your Supabase database
-- 2. This will clear existing data and insert clean data
-- 3. Then use your existing /admin/sync for intake dates

-- Clear all existing course data (optional - remove this if you want to keep existing data)
DELETE FROM courses;

-- Reset the sequence (optional)
ALTER SEQUENCE courses_id_seq RESTART WITH 1;


-- ==============================
-- AIBT College Courses
-- ==============================

-- Sample data structure for AIBT
-- INSERT INTO courses (college_name, faculty, course_name, vet_code, cricos_code, created_at, updated_at) VALUES
-- ('AIBT', 'FACULTY_NAME', 'COURSE_NAME', 'VET_CODE', 'CRICOS_CODE', NOW(), NOW());

-- TODO: Add actual data for AIBT here
-- You can paste the data I'll generate for each sheet individually


-- ==============================
-- REACH College Courses
-- ==============================

-- Sample data structure for REACH
-- INSERT INTO courses (college_name, faculty, course_name, vet_code, cricos_code, created_at, updated_at) VALUES
-- ('REACH', 'FACULTY_NAME', 'COURSE_NAME', 'VET_CODE', 'CRICOS_CODE', NOW(), NOW());

-- TODO: Add actual data for REACH here
-- You can paste the data I'll generate for each sheet individually


-- ==============================
-- NPA College Courses
-- ==============================

-- Sample data structure for NPA
-- INSERT INTO courses (college_name, faculty, course_name, vet_code, cricos_code, created_at, updated_at) VALUES
-- ('NPA', 'FACULTY_NAME', 'COURSE_NAME', 'VET_CODE', 'CRICOS_CODE', NOW(), NOW());

-- TODO: Add actual data for NPA here
-- You can paste the data I'll generate for each sheet individually


-- ==============================
-- AVT College Courses
-- ==============================

-- Sample data structure for AVT
-- INSERT INTO courses (college_name, faculty, course_name, vet_code, cricos_code, created_at, updated_at) VALUES
-- ('AVT', 'FACULTY_NAME', 'COURSE_NAME', 'VET_CODE', 'CRICOS_CODE', NOW(), NOW());

-- TODO: Add actual data for AVT here
-- You can paste the data I'll generate for each sheet individually


-- ==============================
-- AAIBT College Courses
-- ==============================

-- Sample data structure for AAIBT
-- INSERT INTO courses (college_name, faculty, course_name, vet_code, cricos_code, created_at, updated_at) VALUES
-- ('AAIBT', 'FACULTY_NAME', 'COURSE_NAME', 'VET_CODE', 'CRICOS_CODE', NOW(), NOW());

-- TODO: Add actual data for AAIBT here
-- You can paste the data I'll generate for each sheet individually


-- ==============================
-- IBIC College Courses
-- ==============================

-- Sample data structure for IBIC
-- INSERT INTO courses (college_name, faculty, course_name, vet_code, cricos_code, created_at, updated_at) VALUES
-- ('IBIC', 'FACULTY_NAME', 'COURSE_NAME', 'VET_CODE', 'CRICOS_CODE', NOW(), NOW());

-- TODO: Add actual data for IBIC here
-- You can paste the data I'll generate for each sheet individually


-- ==============================
-- Verification Queries
-- ==============================

-- Check total courses imported
SELECT college_name, COUNT(*) as course_count 
FROM courses 
GROUP BY college_name 
ORDER BY college_name;

-- Check for any invalid CRICOS codes
SELECT college_name, course_name, cricos_code 
FROM courses 
WHERE cricos_code IS NULL OR cricos_code = '' OR cricos_code LIKE 'CR%';

-- Check total count
SELECT COUNT(*) as total_courses FROM courses;

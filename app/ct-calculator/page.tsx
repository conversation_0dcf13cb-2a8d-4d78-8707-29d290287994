'use client';

import { useState, useRef } from 'react';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import CTChatInterface from './components/CTChatInterface';
import CTDataSidebar from './components/CTDataSidebar';
import CTResultsCard from './components/CTResultsCard';
import CTRecentCalculations from './components/CTRecentCalculations';
import CTSaveDialog from './components/CTSaveDialog';
import CTTemplates from './components/CTTemplates';
import { CTCalculationResult } from '@/lib/ai/ct-calculator';
import { saveCTCalculation, type CTCalculationRecord } from '@/lib/supabase/ctCalculationService';
import { MessageSquare, Calculator, Sparkles, History, Zap } from 'lucide-react';
import ProtectedRoute from "@/components/ProtectedRoute";
import Sidebar from "@/components/Sidebar";

export default function CTCalculatorPage() {
  const [currentCalculation, setCurrentCalculation] = useState<CTCalculationResult | null>(null);
  const [showResults, setShowResults] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const chatInterfaceRef = useRef<{ sendMessage: (message: string) => void } | null>(null);

  const handleCalculationUpdate = (calculation: CTCalculationResult | null) => {
    setCurrentCalculation(calculation);
    setShowResults(!!calculation);
  };

  const handleSaveCalculation = () => {
    if (!currentCalculation) return;
    setShowSaveDialog(true);
  };

  const handleSaveSuccess = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleLoadCalculation = (calculationRecord: CTCalculationRecord) => {
    // Convert the saved calculation back to CTCalculationResult format
    const calculation: CTCalculationResult = calculationRecord.calculation_data;
    setCurrentCalculation(calculation);
    setShowResults(true);
  };

  const handleUseTemplate = (prompt: string) => {
    // Send the template prompt to the chat interface
    chatInterfaceRef.current?.sendMessage(prompt);
  };

  const handleExportReport = () => {
    if (!currentCalculation) return;

    const reportContent = `
CT CALCULATOR REPORT
Generated: ${new Date().toLocaleString()}

STUDENT INFORMATION
${currentCalculation.studentName ? `Student: ${currentCalculation.studentName}` : 'Student: Not specified'}
${currentCalculation.courseName ? `Course: ${currentCalculation.courseName}` : 'Course: Not specified'}

CALCULATION DETAILS
Workflow Type: ${currentCalculation.workflowType}
Original Fee: $${currentCalculation.originalFee.toLocaleString()}
New Fee: $${currentCalculation.newFee.toLocaleString()}
Total Savings: $${currentCalculation.savings.toLocaleString()}

FORMULA
${currentCalculation.formula}

UNITS INFORMATION
${currentCalculation.unitsReduction > 0 ? `Units Credited: ${currentCalculation.unitsReduction}` : 'No units credited (First Aid CT)'}

COMPLIANCE REQUIREMENTS
☐ Student written agreement received
☐ Supporting documentation verified
☐ FLOO promotional restrictions checked
${currentCalculation.workflowType === 'First Aid CT' ? '☐ First Aid certificate verified\n☐ Invoice amount confirmed' : ''}
${currentCalculation.workflowType === 'General CT' ? '☐ CT Form completed\n☐ Unit mapping confirmed' : ''}
${currentCalculation.workflowType === 'Change of Qualification' ? '☐ COQ Application submitted\n☐ Academic Progression Report reviewed' : ''}

PORTAL INSTRUCTIONS
• Update 'Total Tuition Fee' to $${currentCalculation.newFee.toLocaleString()}
• Add CT application note to student record
${currentCalculation.unitsReduction > 0 ? '• Update course end date' : '• Note: Course duration unchanged (First Aid CT)'}

PRISMS INSTRUCTIONS
• Cancel existing CoE
• Issue new CoE with updated fee: $${currentCalculation.newFee.toLocaleString()}
${currentCalculation.unitsReduction > 0 ? '• Update course duration' : '• Note: No duration change required'}
• Submit variation to PRISMS

---
Generated by EduPace CT Calculator
`;

    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ct-report-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <ProtectedRoute>
      <div className="flex h-screen overflow-hidden bg-white">
        <Sidebar />
        <main className="flex-1 overflow-auto bg-gradient-to-br from-slate-50 to-blue-50">
          <div className="min-h-full pt-16 sm:pt-18 md:pt-20 xl:pt-0">
            <div className="animate-fade-in-up">
              {/* Compact Header */}
              <div className="px-6 py-6 border-b border-white/20 bg-white/60 backdrop-blur-sm">
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg shadow-lg">
                    <MessageSquare className="w-6 h-6 text-white" />
                  </div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                    CT Calculator
                  </h1>
                  <span className="px-2 py-1 text-xs font-medium bg-orange-100 text-orange-800 rounded-full border border-orange-200">
                    BETA
                  </span>
                </div>
                <p className="text-slate-600 text-sm max-w-2xl">
                  Credit Transfer Application Assistant with AI guidance for processing CT applications.
                </p>
                <div className="mt-3 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                  <p className="text-orange-800 text-xs font-medium">
                    ⚠️ Please use carefully - outputs may not always be accurate. Always verify calculations and review compliance requirements.
                  </p>
                </div>
              </div>

              {/* Main Content */}
              <div className="p-6">
                <div className="space-y-6">
                  {/* Chat Interface - Full Width */}
                  <div className="space-y-6">
                    <Card variant="elevated" padding="none" className="h-[700px]">
                      <CTChatInterface
                        ref={chatInterfaceRef}
                        onCalculationUpdate={handleCalculationUpdate}
                      />
                    </Card>

                    {/* Results Section - Full Width Below Chat */}
                    {showResults && currentCalculation && (
                      <CTResultsCard
                        calculation={currentCalculation}
                        onExport={handleExportReport}
                        onSave={handleSaveCalculation}
                      />
                    )}
                  </div>

                  {/* Tools Section - Full Width Below Chat and Results */}
                  <div className="space-y-6">
                    <Tabs defaultValue="tools" className="w-full">
                      <TabsList className="grid w-full grid-cols-3 max-w-md">
                        <TabsTrigger value="tools" className="text-xs">
                          <Calculator className="w-4 h-4 mr-1" />
                          Tools
                        </TabsTrigger>
                        <TabsTrigger value="history" className="text-xs">
                          <History className="w-4 h-4 mr-1" />
                          History
                        </TabsTrigger>
                        <TabsTrigger value="templates" className="text-xs">
                          <Zap className="w-4 h-4 mr-1" />
                          Templates
                        </TabsTrigger>
                      </TabsList>

                      <TabsContent value="tools" className="mt-4">
                        <CTDataSidebar
                          calculation={currentCalculation}
                          onSaveCalculation={handleSaveCalculation}
                          onExportReport={handleExportReport}
                        />
                      </TabsContent>

                      <TabsContent value="history" className="mt-4">
                        <CTRecentCalculations
                          onLoadCalculation={handleLoadCalculation}
                          refreshTrigger={refreshTrigger}
                        />
                      </TabsContent>

                      <TabsContent value="templates" className="mt-4">
                        <CTTemplates onUseTemplate={handleUseTemplate} />
                      </TabsContent>
                    </Tabs>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </main>

        {/* Save Dialog */}
        {currentCalculation && (
          <CTSaveDialog
            calculation={currentCalculation}
            isOpen={showSaveDialog}
            onClose={() => setShowSaveDialog(false)}
            onSaved={handleSaveSuccess}
          />
        )}
      </div>
    </ProtectedRoute>
  );
}

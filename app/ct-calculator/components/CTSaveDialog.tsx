'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Save, 
  X, 
  User, 
  BookOpen, 
  DollarSign,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { CTCalculationResult } from '@/lib/ai/ct-calculator';
import { saveCTCalculation } from '@/lib/supabase/ctCalculationService';

interface CTSaveDialogProps {
  calculation: CTCalculationResult;
  isOpen: boolean;
  onClose: () => void;
  onSaved: () => void;
}

export default function CTSaveDialog({ calculation, isOpen, onClose, onSaved }: CTSaveDialogProps) {
  const [title, setTitle] = useState(() => {
    const defaultTitle = `${calculation.workflowType} - ${
      calculation.studentName || 'Student'
    } - ${new Date().toLocaleDateString()}`;
    return defaultTitle;
  });
  const [notes, setNotes] = useState('');
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getWorkflowColor = (workflowType: string) => {
    switch (workflowType) {
      case 'First Aid CT':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'General CT':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Change of Qualification':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleSave = async () => {
    if (!title.trim()) {
      setError('Please enter a title for this calculation');
      return;
    }

    try {
      setSaving(true);
      setError(null);

      const result = await saveCTCalculation({
        title: title.trim(),
        calculation,
        notes: notes.trim() || `Saved from CT Calculator on ${new Date().toLocaleString()}`
      });

      if (result.success) {
        onSaved();
        onClose();
      } else {
        setError(result.error || 'Failed to save calculation');
      }
    } catch (err) {
      console.error('Error saving calculation:', err);
      setError('Unexpected error occurred while saving');
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    if (!saving) {
      onClose();
      setError(null);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Save className="w-5 h-5 text-blue-600" />
              Save Calculation
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              disabled={saving}
              className="h-8 w-8 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Calculation Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-3">Calculation Summary</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Badge className={`mb-2 ${getWorkflowColor(calculation.workflowType)}`}>
                  {calculation.workflowType}
                </Badge>
                {calculation.studentName && (
                  <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                    <User className="w-4 h-4" />
                    <span>{calculation.studentName}</span>
                  </div>
                )}
                {calculation.courseName && (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <BookOpen className="w-4 h-4" />
                    <span>{calculation.courseName}</span>
                  </div>
                )}
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-600 mb-1">Total Savings</div>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(calculation.savings)}
                </div>
                <div className="text-xs text-gray-500">
                  {((calculation.savings / calculation.originalFee) * 100).toFixed(1)}% reduction
                </div>
              </div>
            </div>
          </div>

          {/* Title Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Calculation Title *
            </label>
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter a descriptive title..."
              className="w-full"
              disabled={saving}
            />
            <p className="text-xs text-gray-500">
              Choose a title that helps you identify this calculation later
            </p>
          </div>

          {/* Notes Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Notes (Optional)
            </label>
            <Textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Add any additional notes or context..."
              className="w-full min-h-[80px]"
              disabled={saving}
            />
            <p className="text-xs text-gray-500">
              Add context, special considerations, or follow-up actions
            </p>
          </div>

          {/* Error Display */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertCircle className="w-4 h-4 text-red-600 flex-shrink-0" />
              <span className="text-sm text-red-700">{error}</span>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={saving}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={saving || !title.trim()}
              className="flex-1"
            >
              {saving ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Save Calculation
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

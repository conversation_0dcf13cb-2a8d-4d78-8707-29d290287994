'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingDown, 
  DollarSign, 
  FileText, 
  CheckSquare, 
  Copy,
  ExternalLink,
  Calculator,
  AlertTriangle
} from 'lucide-react';
import { CTCalculationResult, generateComplianceChecklist, generateInstructions } from '@/lib/ai/ct-calculator';
import { useState } from 'react';

interface CTResultsCardProps {
  calculation: CTCalculationResult;
  onExport?: () => void;
  onSave?: () => void;
}

export default function CTResultsCard({ calculation, onExport, onSave }: CTResultsCardProps) {
  const [copiedSection, setCopiedSection] = useState<string | null>(null);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const copyToClipboard = async (text: string, section: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedSection(section);
      setTimeout(() => setCopiedSection(null), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const getWorkflowColor = (workflowType: string) => {
    switch (workflowType) {
      case 'First Aid CT':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'General CT':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Change of Qualification':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const complianceChecklist = generateComplianceChecklist(calculation.workflowType);
  const instructions = generateInstructions(calculation);

  const savingsPercentage = ((calculation.savings / calculation.originalFee) * 100).toFixed(1);

  return (
    <div className="space-y-6">
      {/* Main Results Summary */}
      <Card variant="elevated" className="border-l-4 border-l-green-500">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Calculator className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <CardTitle className="text-xl">Calculation Results</CardTitle>
                <Badge className={`mt-1 ${getWorkflowColor(calculation.workflowType)}`}>
                  {calculation.workflowType}
                </Badge>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-600">Total Savings</div>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(calculation.savings)}
              </div>
              <div className="text-xs text-gray-500">
                ({savingsPercentage}% reduction)
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-6">
            {/* Original Fee */}
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600 mb-1">Original Fee</div>
              <div className="text-xl font-semibold text-gray-900">
                {formatCurrency(calculation.originalFee)}
              </div>
            </div>

            {/* New Fee */}
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-sm text-blue-600 mb-1">New Fee</div>
              <div className="text-xl font-semibold text-blue-700">
                {formatCurrency(calculation.newFee)}
              </div>
            </div>
          </div>

          {/* Formula Display */}
          <div className="mt-4 p-4 bg-amber-50 rounded-lg border border-amber-200">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="w-4 h-4 text-amber-600" />
              <span className="text-sm font-medium text-amber-900">Calculation Formula</span>
            </div>
            <div className="font-mono text-sm text-amber-800 bg-white p-2 rounded border">
              {calculation.formula}
            </div>
          </div>

          {/* Units Information */}
          {calculation.unitsReduction > 0 && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="text-sm font-medium text-blue-900 mb-1">Units Credited</div>
              <div className="text-blue-700">
                {calculation.unitsReduction} units have been credited, reducing the course duration.
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Compliance Checklist */}
      <Card variant="default">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <CheckSquare className="w-5 h-5 text-blue-600" />
              Compliance Checklist
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(complianceChecklist.map(item => `☐ ${item}`).join('\n'), 'checklist')}
            >
              <Copy className="w-4 h-4" />
              {copiedSection === 'checklist' ? 'Copied!' : 'Copy'}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {complianceChecklist.map((item, index) => (
              <div key={index} className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded">
                <div className="w-4 h-4 border-2 border-gray-300 rounded"></div>
                <span className="text-sm text-gray-700">{item}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Portal Instructions */}
      <Card variant="default">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5 text-green-600" />
              Portal Instructions
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(instructions.portalInstructions.map(item => `• ${item}`).join('\n'), 'portal')}
            >
              <Copy className="w-4 h-4" />
              {copiedSection === 'portal' ? 'Copied!' : 'Copy'}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {instructions.portalInstructions.map((instruction, index) => (
              <div key={index} className="flex items-start gap-3 p-2 hover:bg-gray-50 rounded">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-sm text-gray-700">{instruction}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* PRISMS Instructions */}
      <Card variant="default">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <ExternalLink className="w-5 h-5 text-purple-600" />
              PRISMS Instructions
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(instructions.prismsInstructions.map(item => `• ${item}`).join('\n'), 'prisms')}
            >
              <Copy className="w-4 h-4" />
              {copiedSection === 'prisms' ? 'Copied!' : 'Copy'}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {instructions.prismsInstructions.map((instruction, index) => (
              <div key={index} className="flex items-start gap-3 p-2 hover:bg-gray-50 rounded">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-sm text-gray-700">{instruction}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex gap-3">
        <Button 
          onClick={onExport}
          className="flex-1"
          variant="outline"
        >
          <FileText className="w-4 h-4" />
          Export Report
        </Button>
        <Button 
          onClick={onSave}
          className="flex-1"
          variant="primary"
        >
          <CheckSquare className="w-4 h-4" />
          Save Calculation
        </Button>
      </div>
    </div>
  );
}

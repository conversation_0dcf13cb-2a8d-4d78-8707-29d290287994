'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Calculator, 
  FileText, 
  Download, 
  Save, 
  CheckCircle, 
  AlertCircle,
  DollarSign,
  Users,
  BookOpen,
  Clock
} from 'lucide-react';
import { CTCalculationResult } from '@/lib/ai/ct-calculator';

interface CTDataSidebarProps {
  calculation: CTCalculationResult | null;
  onSaveCalculation?: () => void;
  onExportReport?: () => void;
}

export default function CTDataSidebar({ 
  calculation, 
  onSaveCalculation, 
  onExportReport 
}: CTDataSidebarProps) {
  
  const getWorkflowColor = (workflowType: string) => {
    switch (workflowType) {
      case 'First Aid CT':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'General CT':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Change of Qualification':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Current Calculation Card */}
      {calculation && (
        <Card variant="elevated" className="border-l-4 border-l-blue-600">
          <CardHeader className="pb-4">
            <div className="flex items-center gap-2">
              <Calculator className="w-5 h-5 text-blue-600" />
              <CardTitle className="text-lg">Current Calculation</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Workflow Type */}
            <div>
              <Badge className={`${getWorkflowColor(calculation.workflowType)} font-medium`}>
                {calculation.workflowType}
              </Badge>
            </div>

            {/* Student & Course Info */}
            {(calculation.studentName || calculation.courseName) && (
              <div className="space-y-2">
                {calculation.studentName && (
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600">Student:</span>
                    <span className="font-medium">{calculation.studentName}</span>
                  </div>
                )}
                {calculation.courseName && (
                  <div className="flex items-center gap-2 text-sm">
                    <BookOpen className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600">Course:</span>
                    <span className="font-medium">{calculation.courseName}</span>
                  </div>
                )}
              </div>
            )}

            {/* Financial Summary */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Original Fee:</span>
                <span className="font-semibold text-gray-900">
                  {formatCurrency(calculation.originalFee)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">New Fee:</span>
                <span className="font-semibold text-blue-600">
                  {formatCurrency(calculation.newFee)}
                </span>
              </div>
              <div className="border-t pt-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-green-700">Total Savings:</span>
                  <span className="font-bold text-green-700">
                    {formatCurrency(calculation.savings)}
                  </span>
                </div>
              </div>
            </div>

            {/* Units Information */}
            {calculation.unitsReduction > 0 && (
              <div className="bg-blue-50 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">Units Credited</span>
                </div>
                <div className="text-sm text-blue-700">
                  {calculation.unitsReduction} units credited
                </div>
              </div>
            )}

            {/* Formula */}
            <div className="bg-amber-50 rounded-lg p-3">
              <div className="flex items-center gap-2 mb-2">
                <AlertCircle className="w-4 h-4 text-amber-600" />
                <span className="text-sm font-medium text-amber-900">Calculation Formula</span>
              </div>
              <div className="text-xs text-amber-700 font-mono">
                {calculation.formula}
              </div>
            </div>

            {/* Timestamp */}
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <Clock className="w-3 h-3" />
              <span>Calculated: {calculation.calculatedAt.toLocaleString()}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions Card */}
      <Card variant="default">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg flex items-center gap-2">
            <FileText className="w-5 h-5 text-gray-600" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <Button 
            variant="outline" 
            className="w-full justify-start"
            onClick={onExportReport}
            disabled={!calculation}
          >
            <Download className="w-4 h-4" />
            Export Calculation Report
          </Button>
          
          <Button 
            variant="outline" 
            className="w-full justify-start"
            onClick={onSaveCalculation}
            disabled={!calculation}
          >
            <Save className="w-4 h-4" />
            Save to Admin Dashboard
          </Button>
          
          <Button 
            variant="outline" 
            className="w-full justify-start"
            onClick={() => window.open('/admin/saved-calculations', '_blank')}
          >
            <FileText className="w-4 h-4" />
            View Saved Calculations
          </Button>
        </CardContent>
      </Card>

      {/* Workflow Guide Card */}
      <Card variant="glass" className="bg-gradient-to-br from-blue-50 to-indigo-50">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg text-blue-900">CT Workflow Guide</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-sm">
          <div className="space-y-3">
            <div className="p-3 bg-white/60 rounded-lg">
              <div className="font-medium text-green-800 mb-1">First Aid CT</div>
              <div className="text-green-700 text-xs">
                • Duration never changes<br/>
                • Fee reduction = Invoice amount<br/>
                • No unit calculations
              </div>
            </div>
            
            <div className="p-3 bg-white/60 rounded-lg">
              <div className="font-medium text-blue-800 mb-1">General CT</div>
              <div className="text-blue-700 text-xs">
                • Unit-based calculation<br/>
                • Course duration shortens<br/>
                • Formula: (Fee ÷ Total Units) × Remaining
              </div>
            </div>
            
            <div className="p-3 bg-white/60 rounded-lg">
              <div className="font-medium text-purple-800 mb-1">Change of Qualification</div>
              <div className="text-purple-700 text-xs">
                • Same formula as General CT<br/>
                • Multiple courses affected<br/>
                • Study plan restructured
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Compliance Reminder */}
      <Card variant="default" className="border-amber-200 bg-amber-50">
        <CardHeader className="pb-3">
          <CardTitle className="text-base text-amber-900 flex items-center gap-2">
            <AlertCircle className="w-4 h-4" />
            Compliance Reminder
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-xs text-amber-800 space-y-1">
            <div>✓ Student written agreement</div>
            <div>✓ Supporting documentation</div>
            <div>✓ FLOO restrictions checked</div>
            <div>✓ Portal & PRISMS updated</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

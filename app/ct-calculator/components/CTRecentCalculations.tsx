'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  User, 
  DollarSign, 
  Trash2, 
  Eye,
  RefreshCw,
  Calculator
} from 'lucide-react';
import { getCTCalculations, deleteCTCalculation, type CTCalculationRecord } from '@/lib/supabase/ctCalculationService';

interface CTRecentCalculationsProps {
  onLoadCalculation?: (calculation: CTCalculationRecord) => void;
  refreshTrigger?: number;
}

export default function CTRecentCalculations({ onLoadCalculation, refreshTrigger }: CTRecentCalculationsProps) {
  const [calculations, setCalculations] = useState<CTCalculationRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadCalculations = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await getCTCalculations();
      
      if (result.success && result.calculations) {
        // Show only the 5 most recent calculations
        setCalculations(result.calculations.slice(0, 5));
      } else {
        setError(result.error || 'Failed to load calculations');
      }
    } catch (err) {
      setError('Unexpected error loading calculations');
      console.error('Error loading calculations:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCalculations();
  }, [refreshTrigger]);

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this calculation?')) return;

    try {
      const result = await deleteCTCalculation(id);
      if (result.success) {
        setCalculations(prev => prev.filter(calc => calc.id !== id));
      } else {
        alert(`Failed to delete calculation: ${result.error}`);
      }
    } catch (error) {
      console.error('Error deleting calculation:', error);
      alert('Failed to delete calculation');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getWorkflowColor = (workflowType: string) => {
    switch (workflowType) {
      case 'First Aid CT':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'General CT':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Change of Qualification':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      day: '2-digit',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <Card variant="default">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-gray-600" />
            Recent Calculations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-600">Loading...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card variant="default" className="border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-700">
            <Clock className="w-5 h-5" />
            Recent Calculations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="text-red-600 text-sm mb-3">{error}</p>
            <Button variant="outline" size="sm" onClick={loadCalculations}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card variant="default">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-gray-600" />
            Recent Calculations
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={loadCalculations}>
            <RefreshCw className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {calculations.length === 0 ? (
          <div className="text-center py-8">
            <Calculator className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500 text-sm">No calculations yet</p>
            <p className="text-gray-400 text-xs">Start a calculation to see it here</p>
          </div>
        ) : (
          <div className="space-y-3">
            {calculations.map((calc) => (
              <div
                key={calc.id}
                className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {calc.title}
                    </h4>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge className={`text-xs ${getWorkflowColor(calc.workflow_type)}`}>
                        {calc.workflow_type}
                      </Badge>
                    </div>
                  </div>
                  <div className="flex gap-1 ml-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onLoadCalculation?.(calc)}
                      className="h-6 w-6 p-0"
                    >
                      <Eye className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(calc.id)}
                      className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>

                <div className="space-y-1 text-xs text-gray-600">
                  {calc.student_name && (
                    <div className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      <span>{calc.student_name}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-1">
                    <DollarSign className="w-3 h-3" />
                    <span>Saved {formatCurrency(calc.savings)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    <span>{formatDate(calc.created_at)}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Zap,
  Heart,
  GraduationCap,
  RefreshCw,
  DollarSign,
  Users,
  BookOpen
} from 'lucide-react';

interface CTTemplate {
  id: string;
  name: string;
  description: string;
  workflowType: 'First Aid CT' | 'General CT' | 'Change of Qualification';
  icon: React.ReactNode;
  example: string;
  prompt: string;
}

interface CTTemplatesProps {
  onUseTemplate: (prompt: string) => void;
}

const templates: CTTemplate[] = [
  {
    id: 'first-aid',
    name: 'First Aid CT',
    description: 'Quick credit transfer for First Aid units',
    workflowType: 'First Aid CT',
    icon: <Heart className="w-5 h-5" />,
    example: 'Student has HLTAID011, original fee $15,000, invoice $500',
    prompt: `Student has completed First Aid training and needs credit transfer.

Student Details:
- Student Name: [Enter student name]
- Course: [Enter course name]
- Original Fee: $[Enter original fee]

First Aid Details:
- Unit Code: HLTAID011 (or specify other First Aid unit)
- Invoice Amount: $[Enter invoice amount]

Please process this First Aid CT application.`
  },
  {
    id: 'general-ct',
    name: 'General CT',
    description: 'Standard unit-based credit transfer',
    workflowType: 'General CT',
    icon: <GraduationCap className="w-5 h-5" />,
    example: '20 units total, 4 credited, original fee $15,000',
    prompt: `Student is applying for General Credit Transfer within their current course.

Student Details:
- Student Name: [Enter student name]
- Course: [Enter course name]
- Original Fee: $[Enter original fee]

Unit Information:
- Total Units in Course: [Enter total units]
- Units to be Credited: [Enter credited units]

Or paste unit table here:
[Paste unit table with credit status]

Please calculate the new fee and provide instructions.`
  },
  {
    id: 'coq',
    name: 'Change of Qualification',
    description: 'Credit transfer with course change',
    workflowType: 'Change of Qualification',
    icon: <RefreshCw className="w-5 h-5" />,
    example: 'Changing from Cert III to Cert IV, 15 units credited',
    prompt: `Student is changing qualification and needs credit transfer assessment.

Student Details:
- Student Name: [Enter student name]
- Current Course: [Enter current course]
- New Course: [Enter new course]
- Original Fee: $[Enter original fee]

Credit Transfer Details:
- Total Units in New Course: [Enter total units]
- Units to be Credited: [Enter credited units]

Please process this Change of Qualification application with credit transfer.`
  }
];

export default function CTTemplates({ onUseTemplate }: CTTemplatesProps) {
  const getWorkflowColor = (workflowType: string) => {
    switch (workflowType) {
      case 'First Aid CT':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'General CT':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Change of Qualification':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Card variant="default">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="w-5 h-5 text-yellow-600" />
          Quick Start Templates
        </CardTitle>
        <p className="text-sm text-gray-600">
          Use these templates to quickly start common CT calculations
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {templates.map((template) => (
            <div
              key={template.id}
              className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${
                    template.workflowType === 'First Aid CT' ? 'bg-green-100 text-green-600' :
                    template.workflowType === 'General CT' ? 'bg-blue-100 text-blue-600' :
                    'bg-purple-100 text-purple-600'
                  }`}>
                    {template.icon}
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{template.name}</h3>
                    <p className="text-sm text-gray-600">{template.description}</p>
                  </div>
                </div>
                <Badge className={`${getWorkflowColor(template.workflowType)} text-xs`}>
                  {template.workflowType}
                </Badge>
              </div>

              <div className="mb-3">
                <div className="text-xs text-gray-500 mb-1">Example:</div>
                <div className="text-sm text-gray-700 italic">
                  &ldquo;{template.example}&rdquo;
                </div>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => onUseTemplate(template.prompt)}
                className="w-full"
              >
                <Zap className="w-4 h-4 mr-2" />
                Use This Template
              </Button>
            </div>
          ))}
        </div>

        {/* Quick Tips */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="font-medium text-blue-900 mb-2">💡 Quick Tips</h4>
          <div className="space-y-1 text-sm text-blue-800">
            <div>• Templates provide structured prompts for common scenarios</div>
            <div>• Replace bracketed placeholders with actual values</div>
            <div>• You can modify the template text before sending</div>
            <div>• Paste unit tables directly for automatic parsing</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

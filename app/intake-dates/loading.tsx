export default function Loading() {
  return (
    <div className="p-8">
      <div className="max-w-6xl mx-auto">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-700 rounded w-1/3 mb-8"></div>
          
          <div className="card mb-8">
            <div className="h-6 bg-gray-700 rounded w-1/4 mb-4"></div>
            <div className="h-4 bg-gray-700 rounded w-3/4"></div>
          </div>
          
          <div className="space-y-6">
            <div className="card">
              <div className="h-6 bg-gray-700 rounded w-1/4 mb-4"></div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="h-10 bg-gray-700 rounded"></div>
                <div className="h-10 bg-gray-700 rounded"></div>
                <div className="h-10 bg-gray-700 rounded"></div>
              </div>
            </div>
            
            {[1, 2, 3].map((i) => (
              <div key={i} className="card">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-10 h-10 bg-gray-700 rounded-full"></div>
                  <div>
                    <div className="h-5 bg-gray-700 rounded w-24 mb-2"></div>
                    <div className="h-3 bg-gray-700 rounded w-16"></div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  {[1, 2].map((j) => (
                    <div key={j} className="bg-gray-900/50 rounded-lg p-4 border border-gray-700">
                      <div className="h-4 bg-gray-700 rounded w-3/4 mb-4"></div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <div className="h-4 bg-gray-700 rounded w-1/4"></div>
                          <div className="space-y-1">
                            <div className="h-8 bg-gray-700 rounded"></div>
                            <div className="h-8 bg-gray-700 rounded"></div>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="h-4 bg-gray-700 rounded w-1/4"></div>
                          <div className="space-y-1">
                            <div className="h-8 bg-gray-700 rounded"></div>
                            <div className="h-8 bg-gray-700 rounded"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
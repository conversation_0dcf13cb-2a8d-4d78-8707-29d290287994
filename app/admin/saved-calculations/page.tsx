"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import ProtectedRoute from "@/components/ProtectedRoute";
import Sidebar from "@/components/Sidebar";
import {
  Database,
  Search,
  Eye,
  Edit,
  Trash2,
  Download,
  RefreshCw,
  Calendar,
  Users,
  Calculator,
  Filter,
  ChevronDown,
  FileText,
  FileSpreadsheet
} from "lucide-react";
import {
  getDefermentCalculations,
  deleteDefermentCalculation,
  updateDefermentCalculation,
  type CalculationWithCourses,
  convertDatabaseCourseToComponent
} from "@/lib/supabase/defermentService";

export default function SavedCalculationsAdmin() {
  const [calculations, setCalculations] = useState<CalculationWithCourses[]>([]);
  const [filteredCalculations, setFilteredCalculations] = useState<CalculationWithCourses[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCalculation, setSelectedCalculation] = useState<CalculationWithCourses | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [editingCalculation, setEditingCalculation] = useState<CalculationWithCourses | null>(null);
  const [editTitle, setEditTitle] = useState("");
  const [editNotes, setEditNotes] = useState("");
  const [filterPaymentCalc, setFilterPaymentCalc] = useState<"all" | "yes" | "no">("all");
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);

  // Load calculations on component mount
  useEffect(() => {
    loadCalculations();
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setOpenDropdownId(null);
    };

    if (openDropdownId) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [openDropdownId]);

  // Filter calculations based on search term and payment calculation filter
  useEffect(() => {
    let filtered = calculations;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(calc =>
        calc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        calc.notes?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        calc.courses.some(course =>
          course.course_name.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    // Filter by payment calculation
    if (filterPaymentCalc !== "all") {
      filtered = filtered.filter(calc =>
        filterPaymentCalc === "yes" ? calc.include_payment_calculation : !calc.include_payment_calculation
      );
    }

    setFilteredCalculations(filtered);
  }, [calculations, searchTerm, filterPaymentCalc]);

  const loadCalculations = async () => {
    setLoading(true);
    try {
      const result = await getDefermentCalculations();
      if (result.success && result.calculations) {
        setCalculations(result.calculations);
      } else {
        console.error("Failed to load calculations:", result.error);
        alert("Failed to load calculations: " + result.error);
      }
    } catch (error) {
      console.error("Error loading calculations:", error);
      alert("An error occurred while loading calculations");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string, title: string) => {
    if (!confirm(`Are you sure you want to delete "${title}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const result = await deleteDefermentCalculation(id);
      if (result.success) {
        setCalculations(prev => prev.filter(calc => calc.id !== id));
        alert("Calculation deleted successfully");
      } else {
        alert("Failed to delete calculation: " + result.error);
      }
    } catch (error) {
      console.error("Error deleting calculation:", error);
      alert("An error occurred while deleting the calculation");
    }
  };

  const handleEdit = (calculation: CalculationWithCourses) => {
    setEditingCalculation(calculation);
    setEditTitle(calculation.title);
    setEditNotes(calculation.notes || "");
  };

  const handleSaveEdit = async () => {
    if (!editingCalculation) return;

    try {
      const result = await updateDefermentCalculation(editingCalculation.id, {
        title: editTitle,
        notes: editNotes
      });

      if (result.success) {
        setCalculations(prev => prev.map(calc =>
          calc.id === editingCalculation.id
            ? { ...calc, title: editTitle, notes: editNotes }
            : calc
        ));
        setEditingCalculation(null);
        alert("Calculation updated successfully");
      } else {
        alert("Failed to update calculation: " + result.error);
      }
    } catch (error) {
      console.error("Error updating calculation:", error);
      alert("An error occurred while updating the calculation");
    }
  };

  const handleViewDetails = (calculation: CalculationWithCourses) => {
    setSelectedCalculation(calculation);
    setShowDetails(true);
  };

  const exportToCSV = () => {
    const headers = [
      "Title", "Created Date", "Updated Date", "Total Courses",
      "Payment Calculation", "Total Deferment Duration (weeks)", "Notes"
    ];

    const csvData = filteredCalculations.map(calc => [
      calc.title,
      new Date(calc.created_at).toLocaleDateString(),
      new Date(calc.updated_at).toLocaleDateString(),
      calc.total_courses,
      calc.include_payment_calculation ? "Yes" : "No",
      calc.total_deferment_duration_weeks || 0,
      calc.notes || ""
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `deferment-calculations-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Generate comprehensive text report for individual calculation
  const generateCalculationReport = (calculation: CalculationWithCourses): string => {
    const timestamp = new Date().toLocaleString();
    let report = `EduPace - Student Deferment Calculator Report\n`;
    report += `Generated on: ${timestamp}\n`;
    report += `Calculation: ${calculation.title}\n`;
    report += `Created: ${new Date(calculation.created_at).toLocaleString()}\n`;
    report += `Last Updated: ${new Date(calculation.updated_at).toLocaleString()}\n`;
    report += `${'='.repeat(60)}\n\n`;

    const sortedCourses = calculation.courses.sort((a, b) => a.course_order - b.course_order);

    sortedCourses.forEach((course, index) => {
      const componentCourse = convertDatabaseCourseToComponent(course);

      report += `COURSE ${course.course_order}: ${course.course_name || `Course ${course.course_order}`}\n`;
      report += `${'-'.repeat(40)}\n\n`;

      // Basic Course Information
      report += `COURSE DETAILS:\n`;
      report += `  Original Start Date: ${course.original_start_date || 'N/A'}\n`;
      report += `  Original End Date: ${course.original_end_date || 'N/A'}\n`;
      report += `  Duration: ${course.duration_weeks || 0} weeks (${course.duration_days || 0} days)\n\n`;

      // Deferment Information (for first course)
      if (course.course_order === 1) {
        report += `DEFERMENT DETAILS:\n`;
        report += `  Deferment Start Date: ${course.deferment_start_date || 'N/A'}\n`;
        report += `  Resumption Date: ${course.resumption_date || 'N/A'}\n`;
        report += `  Deferment Duration: ${course.deferment_duration || 0} weeks\n\n`;
      }

      // New Schedule Information
      report += `NEW SCHEDULE:\n`;
      report += `  New Start Date: ${course.new_start_date || 'N/A'}\n`;
      report += `  New Calculated Start Date: ${course.new_calculated_start_date || 'N/A'}\n`;
      report += `  New End Date: ${course.new_end_date || 'N/A'}\n\n`;

      if (index < sortedCourses.length - 1) {
        report += `\n`;
      }
    });

    if (calculation.notes) {
      report += `NOTES:\n`;
      report += `${calculation.notes}\n\n`;
    }

    report += `${'='.repeat(60)}\n`;
    report += `End of Report\n`;
    report += `Generated by EduPace - Student Deferment Calculator\n`;

    return report;
  };

  // Download individual calculation report as text
  const downloadCalculationReport = (calculation: CalculationWithCourses) => {
    const textContent = generateCalculationReport(calculation);
    const blob = new Blob([textContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `EduPace_${calculation.title.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Download individual calculation as CSV
  const downloadCalculationCSV = (calculation: CalculationWithCourses) => {
    const headers = [
      "Course Order", "Course Name", "Original Start Date", "Original End Date",
      "Duration (Weeks)", "Duration (Days)", "Deferment Start Date", "Resumption Date",
      "Deferment Duration (Weeks)", "New Start Date", "New Calculated Start Date", "New End Date"
    ];

    const sortedCourses = calculation.courses.sort((a, b) => a.course_order - b.course_order);
    const csvData = sortedCourses.map(course => [
      course.course_order,
      course.course_name || `Course ${course.course_order}`,
      course.original_start_date || 'N/A',
      course.original_end_date || 'N/A',
      course.duration_weeks || 0,
      course.duration_days || 0,
      course.deferment_start_date || 'N/A',
      course.resumption_date || 'N/A',
      course.deferment_duration || 0,
      course.new_start_date || 'N/A',
      course.new_calculated_start_date || 'N/A',
      course.new_end_date || 'N/A'
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `EduPace_${calculation.title.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen overflow-hidden bg-white">
          <Sidebar />
          <main className="flex-1 overflow-auto">
            <div className="min-h-full pt-20 lg:pt-0">
              <div className="animate-fade-in-up">
                <div className="p-6">
                  <div className="flex items-center justify-center h-64">
                    <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
                    <span className="ml-2 text-lg text-gray-600">Loading calculations...</span>
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="flex h-screen overflow-hidden bg-white">
        <Sidebar />
        <main className="flex-1 overflow-auto">
          <div className="min-h-full pt-20 lg:pt-0">
            <div className="animate-fade-in-up">
              <div className="min-h-screen bg-gray-50">
                <div className="container mx-auto px-6 py-6 max-w-7xl">
                  {/* Header */}
                  <div className="mb-6 text-center">
                    <div className="flex items-center justify-center gap-3 mb-4">
                      <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-md">
                        <Database className="w-6 h-6 text-white" />
                      </div>
                      <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-800 bg-clip-text text-transparent">
                        Saved Calculations
                      </h1>
                    </div>
                    <p className="max-w-2xl mx-auto text-base text-gray-600">
                      Manage and view all deferment calculations
                    </p>
                  </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card className="bg-white shadow-md border hover:shadow-lg transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Calculator className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Total Calculations</p>
                  <p className="text-xl font-bold text-gray-900">{calculations.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-md border hover:shadow-lg transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Users className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">With Payment Calc</p>
                  <p className="text-xl font-bold text-gray-900">
                    {calculations.filter(c => c.include_payment_calculation).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-md border hover:shadow-lg transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Calendar className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">This Month</p>
                  <p className="text-xl font-bold text-gray-900">
                    {calculations.filter(c =>
                      new Date(c.created_at).getMonth() === new Date().getMonth()
                    ).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-md border hover:shadow-lg transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Filter className="w-5 h-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">Filtered Results</p>
                  <p className="text-xl font-bold text-gray-900">{filteredCalculations.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter Controls */}
        <Card className="bg-white shadow-md border mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <Label htmlFor="search" className="text-sm font-medium text-gray-700 mb-2 block">
                  Search Calculations
                </Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <Input
                    id="search"
                    type="text"
                    placeholder="Search by title, notes, or course name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-10 border border-gray-300 focus:border-blue-500 rounded-md"
                  />
                </div>
              </div>

              <div className="lg:w-48">
                <Label htmlFor="filter" className="text-sm font-medium text-gray-700 mb-2 block">
                  Payment Calculation
                </Label>
                <select
                  id="filter"
                  value={filterPaymentCalc}
                  onChange={(e) => setFilterPaymentCalc(e.target.value as "all" | "yes" | "no")}
                  className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
                >
                  <option value="all">All</option>
                  <option value="yes">With Payment Calc</option>
                  <option value="no">Without Payment Calc</option>
                </select>
              </div>

              <div className="flex gap-2 items-end">
                <Button onClick={loadCalculations} variant="outline" size="sm" className="h-10 px-4">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
                <Button onClick={exportToCSV} variant="outline" size="sm" className="h-10 px-4">
                  <Download className="w-4 h-4 mr-2" />
                  Export CSV
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card className="bg-white shadow-lg rounded-lg overflow-hidden border">
        <CardHeader className="bg-gray-50 border-b p-4">
          <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <div className="p-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-md">
              <Database className="w-4 h-4 text-white" />
            </div>
            Calculations List ({filteredCalculations.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {filteredCalculations.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Title & Notes
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created / Updated
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Courses
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Payment Calc
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Duration
                    </th>
                    <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredCalculations.map((calculation) => (
                    <tr key={calculation.id} className="hover:bg-gray-50 transition-colors duration-150">
                      <td className="px-4 py-3">
                        <div className="max-w-xs">
                          <div className="text-sm font-medium text-gray-900 truncate">
                            {calculation.title}
                          </div>
                          {calculation.notes && (
                            <div className="text-xs text-gray-500 mt-1 truncate">
                              {calculation.notes}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        <div>Created: {new Date(calculation.created_at).toLocaleDateString()}</div>
                        <div>Updated: {new Date(calculation.updated_at).toLocaleDateString()}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-600">
                          <div className="font-medium">{calculation.total_courses} courses</div>
                          {calculation.courses.slice(0, 2).map((course) => (
                            <div key={course.id} className="text-xs text-gray-500 truncate max-w-xs">
                              {course.course_name || `Course ${course.course_order}`}
                            </div>
                          ))}
                          {calculation.courses.length > 2 && (
                            <div className="text-xs text-gray-400">
                              +{calculation.courses.length - 2} more...
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          calculation.include_payment_calculation
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {calculation.include_payment_calculation ? 'Yes' : 'No'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {calculation.total_deferment_duration_weeks || 0} weeks
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="flex items-center justify-center gap-1">
                          <Button
                            onClick={() => handleViewDetails(calculation)}
                            size="sm"
                            variant="outline"
                            className="text-blue-600 hover:bg-blue-50 px-2 py-1 text-xs"
                            title="View Details"
                          >
                            <Eye className="w-3 h-3" />
                          </Button>
                          <div className="relative">
                            <Button
                              onClick={() => setOpenDropdownId(openDropdownId === calculation.id ? null : calculation.id)}
                              size="sm"
                              variant="outline"
                              className="text-purple-600 hover:bg-purple-50 px-2 py-1 text-xs flex items-center gap-1"
                              title="Download Options"
                            >
                              <Download className="w-3 h-3" />
                              <ChevronDown className="w-2 h-2" />
                            </Button>
                            {openDropdownId === calculation.id && (
                              <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]">
                                <button
                                  onClick={() => {
                                    downloadCalculationReport(calculation);
                                    setOpenDropdownId(null);
                                  }}
                                  className="w-full px-3 py-2 text-left text-xs text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                                >
                                  <FileText className="w-3 h-3" />
                                  Text Report
                                </button>
                                <button
                                  onClick={() => {
                                    downloadCalculationCSV(calculation);
                                    setOpenDropdownId(null);
                                  }}
                                  className="w-full px-3 py-2 text-left text-xs text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                                >
                                  <FileSpreadsheet className="w-3 h-3" />
                                  CSV Export
                                </button>
                              </div>
                            )}
                          </div>
                          <Button
                            onClick={() => handleEdit(calculation)}
                            size="sm"
                            variant="outline"
                            className="text-green-600 hover:bg-green-50 px-2 py-1 text-xs"
                            title="Edit"
                          >
                            <Edit className="w-3 h-3" />
                          </Button>
                          <Button
                            onClick={() => handleDelete(calculation.id, calculation.title)}
                            size="sm"
                            variant="outline"
                            className="text-red-600 hover:bg-red-50 px-2 py-1 text-xs"
                            title="Delete"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              <Database className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium">No calculations found</p>
              <p className="text-sm">
                {searchTerm || filterPaymentCalc !== "all"
                  ? "Try adjusting your search or filter criteria"
                  : "No calculations have been saved yet"}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Modal */}
      {editingCalculation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-md bg-white">
            <CardHeader>
              <CardTitle>Edit Calculation</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="editTitle">Title</Label>
                <Input
                  id="editTitle"
                  value={editTitle}
                  onChange={(e) => setEditTitle(e.target.value)}
                  placeholder="Enter calculation title"
                />
              </div>
              <div>
                <Label htmlFor="editNotes">Notes</Label>
                <Textarea
                  id="editNotes"
                  value={editNotes}
                  onChange={(e) => setEditNotes(e.target.value)}
                  placeholder="Enter notes (optional)"
                  rows={3}
                />
              </div>
              <div className="flex gap-2 justify-end">
                <Button
                  onClick={() => setEditingCalculation(null)}
                  variant="outline"
                >
                  Cancel
                </Button>
                <Button onClick={handleSaveEdit}>
                  Save Changes
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Details Modal */}
      {showDetails && selectedCalculation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-4xl bg-white max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Calculation Details: {selectedCalculation.title}</span>
                <Button
                  onClick={() => setShowDetails(false)}
                  variant="ghost"
                  size="sm"
                >
                  ×
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Calculation Info */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Created</Label>
                  <p className="text-sm text-gray-900">{new Date(selectedCalculation.created_at).toLocaleString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Last Updated</Label>
                  <p className="text-sm text-gray-900">{new Date(selectedCalculation.updated_at).toLocaleString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Payment Calculation</Label>
                  <p className="text-sm text-gray-900">{selectedCalculation.include_payment_calculation ? 'Yes' : 'No'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Total Deferment Duration</Label>
                  <p className="text-sm text-gray-900">{selectedCalculation.total_deferment_duration_weeks || 0} weeks</p>
                </div>
              </div>

              {selectedCalculation.notes && (
                <div>
                  <Label className="text-sm font-medium text-gray-700">Notes</Label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded">{selectedCalculation.notes}</p>
                </div>
              )}

              {/* Courses */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-3 block">Courses ({selectedCalculation.courses.length})</Label>
                <div className="space-y-4">
                  {selectedCalculation.courses
                    .sort((a, b) => a.course_order - b.course_order)
                    .map((course) => (
                      <Card key={course.id} className="bg-gray-50">
                        <CardContent className="p-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <Label className="text-xs font-medium text-gray-600">Course Name</Label>
                              <p className="text-sm text-gray-900">{course.course_name || `Course ${course.course_order}`}</p>
                            </div>
                            <div>
                              <Label className="text-xs font-medium text-gray-600">Order</Label>
                              <p className="text-sm text-gray-900">{course.course_order}</p>
                            </div>
                            <div>
                              <Label className="text-xs font-medium text-gray-600">Original Start Date</Label>
                              <p className="text-sm text-gray-900">{course.original_start_date || 'N/A'}</p>
                            </div>
                            <div>
                              <Label className="text-xs font-medium text-gray-600">Original End Date</Label>
                              <p className="text-sm text-gray-900">{course.original_end_date || 'N/A'}</p>
                            </div>
                            <div>
                              <Label className="text-xs font-medium text-gray-600">Deferment Duration</Label>
                              <p className="text-sm text-gray-900">{course.deferment_duration || 0} weeks</p>
                            </div>
                            <div>
                              <Label className="text-xs font-medium text-gray-600">New End Date</Label>
                              <p className="text-sm text-gray-900">{course.new_end_date || 'N/A'}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
                </div>
              </div>
            </div>
          </main>
        </div>
    </ProtectedRoute>
  );
}

"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import ProtectedRoute from "@/components/ProtectedRoute";
import Sidebar from "@/components/Sidebar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  CheckCircle,
  AlertCircle,
  Loader2,
  Upload,
  FileText,
  History,
} from "lucide-react";

export default function SyncPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [uploadedFile, setUploadedFile] = useState<string | null>(null);
  const [fileHistory, setFileHistory] = useState<any[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith(".json")) {
      setError("Please select a JSON file");
      return;
    }

    setIsUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/upload-json", {
        method: "POST",
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Upload failed");
      }

      setUploadedFile(data.fileName);
      loadFileHistory();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Upload failed");
    } finally {
      setIsUploading(false);
    }
  };

  const handleSync = async (fileName?: string) => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch("/api/sync-json", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ fileName: fileName || uploadedFile }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Sync failed");
      }

      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unknown error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const loadFileHistory = async () => {
    try {
      const response = await fetch("/api/file-history");
      const data = await response.json();
      if (response.ok) {
        setFileHistory(data.files || []);
      }
    } catch (err) {
      console.error("Failed to load file history:", err);
    }
  };

  // Load file history on component mount
  useEffect(() => {
    loadFileHistory();
  }, []);

  return (
    <ProtectedRoute>
      <div className="flex h-screen overflow-hidden bg-white">
        <Sidebar />
        <main className="flex-1 overflow-auto">
          <div className="min-h-full pt-20 lg:pt-0">
            <div className="animate-fade-in-up">
              <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold mb-8">Database Sync</h1>

        {/* Upload Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Upload JSON File
            </CardTitle>
            <CardDescription>
              Upload your intake-dates.json file to sync course and intake data
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <input
                ref={fileInputRef}
                type="file"
                accept=".json"
                onChange={handleFileUpload}
                className="hidden"
              />
              <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-sm text-gray-600 mb-4">
                Select your JSON file to upload
              </p>
              <Button
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
                variant="outline"
              >
                {isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Choose File
                  </>
                )}
              </Button>
            </div>

            {uploadedFile && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  File uploaded successfully: <strong>{uploadedFile}</strong>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Sync Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Sync to Database
            </CardTitle>
            <CardDescription>
              Process the uploaded JSON file and sync to Supabase database
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">
                What this sync does:
              </h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Creates new faculties if they don&apos;t exist</li>
                <li>• Updates intake dates for EXISTING courses only</li>
                <li>• Skips courses that don&apos;t exist in the database</li>
                <li>• Preserves data integrity with fail-safe matching</li>
                <li>• Reports errors for missing courses</li>
              </ul>
              <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                <p className="text-xs text-yellow-800">
                  <strong>Important:</strong> This sync will NOT create new courses.
                  Courses must be added to the database with valid CRICOS codes before they can be synced.
                </p>
              </div>
            </div>

            <Button
              onClick={() => handleSync()}
              disabled={isLoading || !uploadedFile}
              className="w-full"
              size="lg"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Syncing...
                </>
              ) : (
                <>
                  <FileText className="mr-2 h-4 w-4" />
                  Sync to Database
                </>
              )}
            </Button>

            {!uploadedFile && (
              <p className="text-sm text-gray-500 text-center">
                Please upload a JSON file first
              </p>
            )}
          </CardContent>
        </Card>

        {/* File History Section */}
        {fileHistory.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                File History
              </CardTitle>
              <CardDescription>
                Previously uploaded files (stored in Supabase Storage)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {fileHistory.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div>
                      <p className="font-medium">{file.name}</p>
                      <p className="text-sm text-gray-500">
                        {new Date(file.created_at).toLocaleString()}
                      </p>
                    </div>
                    <Button
                      onClick={() => handleSync(file.name)}
                      disabled={isLoading}
                      variant="outline"
                      size="sm"
                    >
                      Sync This File
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Results Section */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {result && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-4">
                <p className="font-semibold">Sync completed successfully!</p>

                {/* Summary Stats */}
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="bg-blue-50 p-3 rounded">
                    <p className="font-medium text-blue-900">Faculties</p>
                    <p className="text-blue-700">
                      {result.stats?.providers?.created || 0} created,{" "}
                      {result.stats?.providers?.existing || 0} existing
                    </p>
                  </div>
                  <div className="bg-green-50 p-3 rounded">
                    <p className="font-medium text-green-900">Courses</p>
                    <p className="text-green-700">
                      {result.stats?.courses?.created || 0} created,{" "}
                      {result.stats?.courses?.existing || 0} existing
                    </p>
                  </div>
                  <div className="bg-purple-50 p-3 rounded">
                    <p className="font-medium text-purple-900">Intakes</p>
                    <p className="text-purple-700">
                      {result.stats?.intakes?.created || 0} created
                    </p>
                    {result.stats?.intakes?.invalidDatesSkipped > 0 && (
                      <p className="text-orange-600 text-xs">
                        ({result.stats.intakes.invalidDatesSkipped} invalid
                        dates skipped)
                      </p>
                    )}
                  </div>
                </div>

                {/* Detailed Lists */}
                <div className="space-y-3">
                  {/* Course Errors */}
                  {result.stats?.courses?.errors &&
                    result.stats.courses.errors.length > 0 && (
                      <div>
                        <p className="font-medium text-sm mb-2 text-red-700">
                          Course Errors ({result.stats.courses.errors.length}):
                        </p>
                        <div className="text-xs space-y-1 max-h-40 overflow-y-auto bg-red-50 p-2 rounded border border-red-200">
                          {result.stats.courses.errors.map(
                            (error: string, index: number) => (
                              <div
                                key={index}
                                className="flex items-start gap-2"
                              >
                                <span className="text-red-600 mt-0.5">⚠</span>
                                <span className="text-red-800">{error}</span>
                              </div>
                            )
                          )}
                        </div>
                        <p className="text-xs text-red-600 mt-2">
                          These courses need to be added to the database with valid CRICOS codes before they can be synced.
                        </p>
                      </div>
                    )}

                  {/* Faculties */}
                  {result.stats?.providers?.names &&
                    result.stats.providers.names.length > 0 && (
                      <div>
                        <p className="font-medium text-sm mb-2">Faculties:</p>
                        <div className="text-xs space-y-1 max-h-32 overflow-y-auto bg-gray-50 p-2 rounded">
                          {result.stats.providers.names.map(
                            (name: string, index: number) => (
                              <div
                                key={index}
                                className="flex items-center gap-2"
                              >
                                <span
                                  className={
                                    name.includes("(created)")
                                      ? "text-green-600"
                                      : "text-blue-600"
                                  }
                                >
                                  {name.includes("(created)") ? "+ " : "• "}
                                </span>
                                <span>{name}</span>
                              </div>
                            )
                          )}
                        </div>
                      </div>
                    )}

                  {/* Created Courses */}
                  {result.stats?.courses?.createdList &&
                    result.stats.courses.createdList.length > 0 && (
                      <div>
                        <p className="font-medium text-sm mb-2">
                          New Courses Created (
                          {result.stats.courses.createdList.length}):
                        </p>
                        <div className="text-xs space-y-1 max-h-40 overflow-y-auto bg-green-50 p-2 rounded">
                          {result.stats.courses.createdList
                            .slice(0, 10)
                            .map((course: string, index: number) => (
                              <div
                                key={index}
                                className="flex items-start gap-2"
                              >
                                <span className="text-green-600 mt-0.5">+</span>
                                <span className="text-green-800">{course}</span>
                              </div>
                            ))}
                          {result.stats.courses.createdList.length > 10 && (
                            <p className="text-green-600 font-medium">
                              ... and{" "}
                              {result.stats.courses.createdList.length - 10}{" "}
                              more
                            </p>
                          )}
                        </div>
                      </div>
                    )}

                  {/* Existing Courses */}
                  {result.stats?.courses?.existingList &&
                    result.stats.courses.existingList.length > 0 && (
                      <div>
                        <p className="font-medium text-sm mb-2">
                          Existing Courses Updated (
                          {result.stats.courses.existingList.length}):
                        </p>
                        <div className="text-xs space-y-1 max-h-32 overflow-y-auto bg-blue-50 p-2 rounded">
                          {result.stats.courses.existingList
                            .slice(0, 5)
                            .map((course: string, index: number) => (
                              <div
                                key={index}
                                className="flex items-start gap-2"
                              >
                                <span className="text-blue-600 mt-0.5">•</span>
                                <span className="text-blue-800">{course}</span>
                              </div>
                            ))}
                          {result.stats.courses.existingList.length > 5 && (
                            <p className="text-blue-600 font-medium">
                              ... and{" "}
                              {result.stats.courses.existingList.length - 5}{" "}
                              more
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                </div>
              </div>
            </AlertDescription>
          </Alert>
        )}
      </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}

"use client";

import { useEffect } from "react";
import { AlertTriangle, RefreshCw } from "lucide-react";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error("Deferment Calculator Error:", error);
  }, [error]);

  return (
    <div className="p-8">
      <div className="max-w-6xl mx-auto">
        <div className="card text-center">
          <div className="flex justify-center mb-6">
            <div className="flex items-center justify-center w-16 h-16 bg-red-600 rounded-full">
              <AlertTriangle size={32} className="text-white" />
            </div>
          </div>

          <h1 className="text-2xl font-bold text-white mb-4">
            Something went wrong!
          </h1>

          <p className="text-gray-300 mb-6">
            The deferment calculator encountered an error. This might be due to:
          </p>

          <div className="text-left bg-gray-900/50 rounded-lg p-4 mb-6">
            <ul className="text-gray-300 space-y-2">
              <li>• Invalid date calculations</li>
              <li>• Course data loading issues</li>
              <li>• Network connectivity problems</li>
              <li>• Temporary system issues</li>
            </ul>
          </div>

          <div className="space-y-4">
            <button
              onClick={reset}
              className="btn-primary flex items-center space-x-2 mx-auto"
            >
              <RefreshCw size={16} />
              <span>Try Again</span>
            </button>

            <p className="text-sm text-gray-400">
              If the problem persists, please contact your system administrator.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

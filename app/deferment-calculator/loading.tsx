export default function Loading() {
  return (
    <div className="min-h-screen py-8 animate-fade-in">
      <div className="max-w-6xl mx-auto">
        {/* Header skeleton with gradient shimmer */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-4 mb-6">
            <div
              className="w-10 h-10 bg-gradient-to-br from-primary-500/20 to-accent-500/20 rounded-2xl
                            animate-pulse relative overflow-hidden"
            >
              <div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent
                              animate-shimmer"
              ></div>
            </div>
            <div
              className="h-9 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded-2xl w-80
                            animate-pulse relative overflow-hidden"
            >
              <div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent
                              animate-shimmer"
              ></div>
            </div>
          </div>
          <div
            className="h-6 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded-xl w-96 mx-auto
                          animate-pulse relative overflow-hidden"
          >
            <div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent
                            animate-shimmer"
            ></div>
          </div>
        </div>

        {/* Main content skeleton */}
        <div className="card mb-8 relative overflow-hidden">
          <div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-primary-500/5 to-transparent
                          animate-shimmer"
          ></div>

          {/* Progress indicator */}
          <div className="flex items-center gap-3 mb-8">
            <div
              className="w-8 h-8 bg-gradient-to-br from-primary-500/30 to-accent-500/30 rounded-full
                            animate-pulse flex items-center justify-center"
            >
              <div className="w-4 h-4 bg-primary-400 rounded-full animate-ping"></div>
            </div>
            <div>
              <div
                className="h-5 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded-lg w-48 mb-2
                              animate-pulse"
              ></div>
              <div
                className="h-3 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded w-64
                              animate-pulse"
              ></div>
            </div>
          </div>

          {/* Form sections skeleton */}
          <div className="space-y-8">
            {/* Student Information section */}
            <div className="border-b border-neutral-700/30 pb-8">
              <div className="flex items-center gap-3 mb-6">
                <div
                  className="w-6 h-6 bg-gradient-to-br from-primary-500/30 to-accent-500/30 rounded-lg
                                animate-pulse"
                ></div>
                <div
                  className="h-6 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded-lg w-40
                                animate-pulse"
                ></div>
              </div>
              <div
                className="h-14 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded-2xl
                              animate-pulse relative overflow-hidden max-w-md"
              >
                <div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent
                                animate-shimmer"
                ></div>
              </div>
            </div>

            {/* Date inputs section */}
            <div className="border-b border-neutral-700/30 pb-8">
              <div className="flex items-center gap-3 mb-6">
                <div
                  className="w-6 h-6 bg-gradient-to-br from-primary-500/30 to-accent-500/30 rounded-lg
                                animate-pulse"
                ></div>
                <div
                  className="h-6 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded-lg w-52
                                animate-pulse"
                ></div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="space-y-3">
                    <div
                      className="h-4 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded w-24
                                    animate-pulse"
                    ></div>
                    <div
                      className="h-14 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded-2xl
                                    animate-pulse relative overflow-hidden"
                    >
                      <div
                        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent
                                      animate-shimmer"
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Course selection section */}
            <div className="border-b border-neutral-700/30 pb-8">
              <div className="flex items-center gap-3 mb-6">
                <div
                  className="w-6 h-6 bg-gradient-to-br from-primary-500/30 to-accent-500/30 rounded-lg
                                animate-pulse"
                ></div>
                <div
                  className="h-6 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded-lg w-44
                                animate-pulse"
                ></div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[1, 2].map((i) => (
                  <div key={i} className="space-y-3">
                    <div
                      className="h-4 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded w-20
                                    animate-pulse"
                    ></div>
                    <div
                      className="h-14 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded-2xl
                                    animate-pulse relative overflow-hidden"
                    >
                      <div
                        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent
                                      animate-shimmer"
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              <div
                className="h-14 bg-gradient-to-r from-primary-600/30 to-primary-500/30 rounded-2xl
                              animate-pulse w-full sm:w-40 relative overflow-hidden"
              >
                <div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-primary-400/20 to-transparent
                                animate-shimmer"
                ></div>
              </div>
              <div
                className="h-14 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded-2xl
                              animate-pulse w-full sm:w-32 relative overflow-hidden"
              >
                <div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent
                                animate-shimmer"
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Course cards skeleton */}
        <div className="space-y-6">
          {[1, 2].map((i) => (
            <div key={i} className="card relative overflow-hidden">
              <div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-accent-500/5 to-transparent
                              animate-shimmer"
              ></div>

              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-4">
                  <div
                    className="w-10 h-10 bg-gradient-to-br from-primary-500/30 to-accent-500/30 rounded-full
                                  animate-pulse flex items-center justify-center"
                  >
                    <div className="w-5 h-5 bg-primary-400/50 rounded-full animate-pulse"></div>
                  </div>
                  <div
                    className="h-6 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded-lg w-32
                                  animate-pulse"
                  ></div>
                </div>
                <div
                  className="w-8 h-8 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded-lg
                                animate-pulse"
                ></div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[1, 2, 3, 4].map((j) => (
                  <div key={j} className="space-y-3">
                    <div
                      className="h-4 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded w-28
                                    animate-pulse"
                    ></div>
                    <div
                      className="h-6 bg-gradient-to-r from-neutral-800 to-neutral-700 rounded-lg w-40
                                    animate-pulse"
                    ></div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Loading indicator */}
        <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50">
          <div className="card p-6 flex items-center gap-4 backdrop-blur-xl">
            <div className="relative">
              <div
                className="w-8 h-8 border-4 border-primary-500/30 border-t-primary-500 rounded-full
                              animate-spin"
              ></div>
              <div
                className="absolute inset-0 w-8 h-8 border-4 border-transparent border-r-accent-500
                              rounded-full animate-spin animation-delay-150"
              ></div>
            </div>
            <div>
              <div className="text-white font-semibold">Loading Calculator</div>
              <div className="text-neutral-400 text-sm">
                Preparing your workspace...
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

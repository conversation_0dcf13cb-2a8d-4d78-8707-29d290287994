import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";
import { SidebarThemeProvider } from "@/contexts/SidebarThemeContext";
import ErrorBoundary from "@/components/ErrorBoundary";
import DevTools from "@/components/DevTools";

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: "EduPace",
  description:
    "EduPace - Comprehensive educational platform with tools for course management, deferment calculations, intake dates, and academic planning",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="icon" href="/favicon-32x32.png" type="image/png" sizes="32x32" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" sizes="180x180" />
      </head>
      <body className={inter.className}>
        <ErrorBoundary>
          <AuthProvider>
            <SidebarThemeProvider>
              {children}
            </SidebarThemeProvider>
          </AuthProvider>
          <DevTools version="2.1.0" />
        </ErrorBoundary>
      </body>
    </html>
  );
}

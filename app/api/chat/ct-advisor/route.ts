import { NextRequest, NextResponse } from 'next/server';
import { UniversalAIProvider, AIMessage } from '@/lib/ai/universal-provider';
import { CT_ADVISOR_SYSTEM_PROMPT } from '@/lib/ai/ct-system-prompt';
import { 
  calculateCTFee, 
  parseUnitTable, 
  detectWorkflowType,
  CTCalculationInput,
  CTCalculationResult 
} from '@/lib/ai/ct-calculator';

export async function POST(request: NextRequest) {
  try {
    const { messages } = await request.json();
    
    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { error: 'Invalid messages format' },
        { status: 400 }
      );
    }

    // Initialize AI provider
    const aiProvider = new UniversalAIProvider();
    
    // Prepare messages for AI
    const aiMessages: AIMessage[] = [
      { role: 'system', content: CT_ADVISOR_SYSTEM_PROMPT },
      ...messages
    ];

    // Get AI response
    const response = await aiProvider.sendMessage(aiMessages, {
      temperature: 0.1,
      maxTokens: 2000
    });

    // Try to extract calculation data from the conversation
    let calculations: CTCalculationResult | null = null;
    try {
      calculations = extractCalculationsFromConversation(messages, response.content);
    } catch (error) {
      console.log('No calculations extracted:', error);
      // This is fine - not all messages will have calculations
    }

    return NextResponse.json({
      content: response.content,
      calculations,
      usage: response.usage,
      model: response.model,
      provider: response.provider
    });

  } catch (error) {
    console.error('CT Advisor API Error:', error);
    
    // Return a helpful error message
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return NextResponse.json(
      { 
        error: 'Failed to process CT Advisor request',
        details: errorMessage,
        content: `❌ **Error:** I'm having trouble processing your request right now. Please try again.\n\nError details: ${errorMessage}`
      },
      { status: 500 }
    );
  }
}

/**
 * Extract calculation data from the conversation context
 */
function extractCalculationsFromConversation(
  messages: AIMessage[], 
  aiResponse: string
): CTCalculationResult | null {
  
  // Look for calculation patterns in the AI response
  const calculationMatch = extractCalculationFromResponse(aiResponse);
  if (calculationMatch) {
    return calculationMatch;
  }

  // Try to extract from user messages (unit tables, fee information)
  const userMessages = messages.filter(msg => msg.role === 'user');
  const latestUserMessage = userMessages[userMessages.length - 1];
  
  if (!latestUserMessage) {
    return null;
  }

  return extractCalculationFromUserInput(latestUserMessage.content, aiResponse);
}

/**
 * Extract calculation data from AI response
 */
function extractCalculationFromResponse(response: string): CTCalculationResult | null {
  try {
    // Look for calculation patterns in the response
    const originalFeeMatch = response.match(/Original.*?Fee.*?\$([0-9,]+)/i);
    const newFeeMatch = response.match(/New.*?Fee.*?\$([0-9,]+)/i);
    const savingsMatch = response.match(/Savings.*?\$([0-9,]+)/i);
    const workflowMatch = response.match(/Workflow.*?Identified.*?:\s*(.+?)(?:\n|$)/i);
    const formulaMatch = response.match(/Formula:\s*(.+?)(?:\n|$)/i);

    if (originalFeeMatch && newFeeMatch) {
      const originalFee = parseInt(originalFeeMatch[1].replace(/,/g, ''));
      const newFee = parseInt(newFeeMatch[1].replace(/,/g, ''));
      const savings = savingsMatch ? parseInt(savingsMatch[1].replace(/,/g, '')) : originalFee - newFee;
      const workflowType = workflowMatch?.[1]?.trim() || 'Unknown';
      const formula = formulaMatch?.[1]?.trim() || 'Calculation formula not specified';

      return {
        workflowType,
        originalFee,
        newFee,
        savings,
        formula,
        unitsReduction: 0, // Will be updated if unit info is found
        calculatedAt: new Date()
      };
    }

    return null;
  } catch (error) {
    console.error('Error extracting calculation from response:', error);
    return null;
  }
}

/**
 * Extract calculation data from user input
 */
function extractCalculationFromUserInput(
  userInput: string, 
  aiResponse: string
): CTCalculationResult | null {
  try {
    // Try to parse unit table if present
    let unitData = null;
    if (userInput.toLowerCase().includes('unit') || userInput.includes('HLTAID') || userInput.includes('BSB')) {
      try {
        unitData = parseUnitTable(userInput);
      } catch (error) {
        console.log('Could not parse unit table:', error);
      }
    }

    // Extract fee information from user input
    const feeMatch = userInput.match(/(?:fee|cost|tuition).*?\$([0-9,]+)/i) ||
                    userInput.match(/\$([0-9,]+).*?(?:fee|cost|tuition)/i);
    
    // Extract invoice amount for First Aid
    const invoiceMatch = userInput.match(/invoice.*?\$([0-9,]+)/i) ||
                        userInput.match(/\$([0-9,]+).*?invoice/i);

    if (!feeMatch) {
      return null;
    }

    const originalFee = parseInt(feeMatch[1].replace(/,/g, ''));
    const invoiceAmount = invoiceMatch ? parseInt(invoiceMatch[1].replace(/,/g, '')) : undefined;
    
    // Detect workflow type
    const workflowType = detectWorkflowType(userInput);
    
    // Prepare calculation input
    const calculationInput: CTCalculationInput = {
      originalFee,
      totalUnits: unitData?.totalUnits || 20, // Default assumption
      unitsAfterCT: unitData?.unitsAfterCT || 16, // Default assumption
      invoiceAmount,
      workflowType,
    };

    // Extract student/course names if present
    const studentMatch = userInput.match(/student.*?:?\s*([A-Za-z\s]+)/i);
    const courseMatch = userInput.match(/course.*?:?\s*([A-Za-z0-9\s]+)/i);

    if (studentMatch) {
      calculationInput.studentName = studentMatch[1].trim();
    }
    if (courseMatch) {
      calculationInput.courseName = courseMatch[1].trim();
    }

    // Calculate the result
    const result = calculateCTFee(calculationInput);
    
    return result;

  } catch (error) {
    console.error('Error extracting calculation from user input:', error);
    return null;
  }
}

/**
 * Health check endpoint
 */
export async function GET() {
  try {
    // Test AI provider connection
    const aiProvider = new UniversalAIProvider();
    const isConnected = await aiProvider.testConnection();
    
    return NextResponse.json({
      status: 'healthy',
      aiProvider: {
        connected: isConnected,
        provider: aiProvider.getConfig().provider,
        model: aiProvider.getConfig().modelName
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json(
      { 
        status: 'unhealthy', 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Google Sheets configuration
const SPREADSHEET_ID = '10_aDbphk8Hn_XsJF2o_BbKEb4u48uJCPKNXSJag3Z_k';
const COLLEGE_SHEETS = ['AIBT', 'REACH', 'NPA', 'AVT', 'AAIBT', 'IBIC'];

interface CourseData {
  Faculty: string;
  'Course Name': string;
  'VET Code': string;
  'CRICOS Code': string;
}

interface SheetData {
  values: string[][];
}

// Function to fetch actual Google Sheets data using server-side integration
async function fetchGoogleSheetData(sheetName: string): Promise<CourseData[]> {
  try {
    // This would need to be implemented with server-side Google Sheets integration
    // For now, we'll use a hybrid approach with the existing structure

    // Since we're in a server environment, we'll need to implement
    // the Google Sheets API integration differently
    console.log(`Fetching data from sheet: ${sheetName}`);

    // Placeholder for actual Google Sheets API integration
    // This would typically use the Google Sheets API with proper authentication
    return [];
  } catch (error) {
    console.error(`Error fetching sheet ${sheetName}:`, error);
    return [];
  }
}

function isValidCRICOSCode(code: string): boolean {
  // Check if CRICOS code is valid (not fake)
  // Fake codes usually start with 'CR' or are obviously fake
  if (!code || code === 'N/A' || code.startsWith('CR')) {
    return false;
  }

  // Valid CRICOS codes are usually 6 characters with letter at the end
  const cricosPattern = /^\d{5}[A-Z]$/;
  return cricosPattern.test(code);
}

export async function POST(request: Request) {
  try {
    const { clearCorruptedData } = await request.json();
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const results = {
      sheets: [] as string[],
      totalCourses: 0,
      skippedCourses: 0,
      errors: [] as string[]
    };

    // Clear corrupted data if requested
    if (clearCorruptedData) {
      try {
        const { error: deleteError } = await supabase
          .from('courses')
          .delete()
          .like('cricos_code', 'CR%');

        if (deleteError) {
          results.errors.push(`Failed to clear corrupted data: ${deleteError.message}`);
        }
      } catch (error) {
        results.errors.push(`Error clearing corrupted data: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Process each college sheet
    for (const sheetName of COLLEGE_SHEETS) {
      try {
        const sheetData = await fetchGoogleSheetData(sheetName);
        results.sheets.push(sheetName);

        for (const course of sheetData) {
          // Validate CRICOS code
          if (!isValidCRICOSCode(course['CRICOS Code'])) {
            results.skippedCourses++;
            continue;
          }

          // Prepare course data for database
          const courseData = {
            college_name: sheetName,
            faculty: course.Faculty,
            course_name: course['Course Name'],
            vet_code: course['VET Code'] === 'N/A' ? null : course['VET Code'],
            cricos_code: course['CRICOS Code'],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          // Insert or update course
          const { error: upsertError } = await supabase
            .from('courses')
            .upsert(courseData, {
              onConflict: 'college_name,course_name'
            });

          if (upsertError) {
            results.errors.push(`Failed to insert course "${course['Course Name']}": ${upsertError.message}`);
            results.skippedCourses++;
          } else {
            results.totalCourses++;
          }
        }
      } catch (error) {
        results.errors.push(`Failed to process sheet "${sheetName}": ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Successfully imported ${results.totalCourses} courses from ${results.sheets.length} sheets`,
      details: results
    });

  } catch (error) {
    console.error('Google Sheets import error:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to import data from Google Sheets',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

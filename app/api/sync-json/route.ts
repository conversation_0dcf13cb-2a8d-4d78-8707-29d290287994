import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Starting JSON sync via API...');
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const body = await request.json();
    const fileName = body.fileName;

    if (!fileName) {
      return NextResponse.json({ error: 'No file specified' }, { status: 400 });
    }

    // Download file from Supabase Storage
    const { data: fileData, error: downloadError } = await supabase.storage
      .from('json-files')
      .download(fileName);

    if (downloadError) {
      console.error('Download error:', downloadError);
      return NextResponse.json({ error: 'Failed to download file' }, { status: 500 });
    }

    // Convert blob to text
    const jsonContent = await fileData.text();

    // Validate JSON
    let jsonData;
    try {
      jsonData = JSON.parse(jsonContent);
    } catch (error) {
      return NextResponse.json({ error: 'Invalid JSON content' }, { status: 400 });
    }

    // Process the JSON data and sync to database
    const stats = await syncJsonToDatabase(jsonData, supabase);

    return NextResponse.json({
      success: true,
      message: 'Sync completed successfully',
      stats,
      fileName
    });

  } catch (error) {
    console.error('Sync failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function syncJsonToDatabase(jsonData: any, supabase: any) {
  const stats = {
    providers: { created: 0, existing: 0, names: [] as string[] },
    courses: { created: 0, existing: 0, updated: 0, createdList: [] as string[], existingList: [] as string[], errors: [] as string[] },
    intakes: { created: 0, deleted: 0, invalidDatesSkipped: 0 }
  };

  try {
    for (const college of jsonData) {
      const collegeName = college.collegeName;

      // Check if faculty exists (your DB uses faculties, not providers)
      const { data: existingFaculty } = await supabase
        .from('faculties')
        .select('id')
        .eq('name', collegeName)
        .single();

      let facultyId;
      if (existingFaculty) {
        facultyId = existingFaculty.id;
        stats.providers.existing++;
        stats.providers.names.push(`${collegeName} (existing)`);
      } else {
        // Create new faculty
        const { data: newFaculty, error } = await supabase
          .from('faculties')
          .insert({ name: collegeName })
          .select('id')
          .single();

        if (error) throw error;
        facultyId = newFaculty.id;
        stats.providers.created++;
        stats.providers.names.push(`${collegeName} (created)`);
      }

      // Process courses for this provider
      for (const course of college.courses) {
        const courseName = course.courseName;

        // FAIL-SAFE APPROACH: Only update existing courses, never create new ones
        const { data: existingCourse } = await supabase
          .from('courses')
          .select('id')
          .eq('course_name', courseName)
          .eq('faculty_id', facultyId)
          .single();

        let courseId;
        if (existingCourse) {
          courseId = existingCourse.id;
          stats.courses.existing++;
          stats.courses.existingList.push(`${courseName} (${collegeName})`);
        } else {
          // STOP: Do not create new courses with fake CRICOS codes
          const errorMessage = `Course '${courseName}' for college '${collegeName}' was not found in the database. Please add it manually with the correct CRICOS code before syncing.`;
          stats.courses.errors.push(errorMessage);
          console.warn(errorMessage);
          continue; // Skip this course and continue with the next one
        }

        // Delete existing intakes for this course
        const { error: deleteError } = await supabase
          .from('intakes')
          .delete()
          .eq('course_id', courseId);

        if (deleteError) throw deleteError;

        // We need a default location_id. Let's check if locations exist
        const { data: locations } = await supabase
          .from('locations')
          .select('id')
          .limit(1);

        let locationId;
        if (locations && locations.length > 0) {
          locationId = locations[0].id;
        } else {
          // Create a default location
          const { data: newLocation, error: locationError } = await supabase
            .from('locations')
            .insert({ name: 'Default Location' })
            .select('id')
            .single();

          if (locationError) throw locationError;
          locationId = newLocation.id;
        }

        // Insert new intakes (filter out invalid dates)
        const validIntakes = course.intakes.filter((intake: any) => {
          const date = intake.date;
          // Skip invalid dates like "1900-01-00"
          if (!date || date === '1900-01-00' || date.includes('00')) {
            stats.intakes.invalidDatesSkipped++;
            return false;
          }
          // Validate date format
          const dateObj = new Date(date);
          if (isNaN(dateObj.getTime()) || dateObj.getFullYear() <= 1900) {
            stats.intakes.invalidDatesSkipped++;
            return false;
          }
          return true;
        });

        const intakeInserts = validIntakes.map((intake: any) => ({
          course_id: courseId,
          location_id: locationId,
          intake_date: intake.date
        }));

        if (intakeInserts.length > 0) {
          const { error: insertError } = await supabase
            .from('intakes')
            .insert(intakeInserts);

          if (insertError) throw insertError;
          stats.intakes.created += intakeInserts.length;
        }
      }
    }

    return stats;
  } catch (error) {
    console.error('Database sync error:', error);
    throw error;
  }
}

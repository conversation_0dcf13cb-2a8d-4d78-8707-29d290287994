import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET(request: NextRequest) {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({ error: 'Supabase configuration missing' }, { status: 500 });
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    // List files from the json-files bucket
    const { data, error } = await supabase.storage
      .from('json-files')
      .list('', {
        sortBy: { column: 'created_at', order: 'desc' }
      });

    if (error) {
      console.error('Storage list error:', error);
      return NextResponse.json({ error: 'Failed to list files' }, { status: 500 });
    }

    // Filter out any non-JSON files (just in case)
    const jsonFiles = data.filter(file => file.name.endsWith('.json'));

    return NextResponse.json({
      success: true,
      files: jsonFiles
    });

  } catch (error) {
    console.error('File history error:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve file history' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Starting Google Sheets import...');
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const body = await request.json();
    const { sheetsData, clearExisting = false } = body;

    if (!sheetsData || typeof sheetsData !== 'object') {
      return NextResponse.json({ error: 'Invalid sheets data provided' }, { status: 400 });
    }

    // Clear existing corrupted data if requested
    if (clearExisting) {
      await clearCorruptedData(supabase);
    }

    // Import clean data from Google Sheets
    const stats = await importSheetsData(sheetsData, supabase);

    return NextResponse.json({
      success: true,
      message: 'Google Sheets import completed successfully',
      stats
    });

  } catch (error) {
    console.error('Google Sheets import failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function clearCorruptedData(supabase: any) {
  console.log('🧹 Clearing corrupted course data...');
  
  try {
    // Get all courses with fake CRICOS codes (those starting with 'CR')
    const { data: corruptedCourses, error: selectError } = await supabase
      .from('courses')
      .select('id, course_name, cricos_code')
      .like('cricos_code', 'CR%');

    if (selectError) throw selectError;

    if (corruptedCourses && corruptedCourses.length > 0) {
      console.log(`Found ${corruptedCourses.length} corrupted courses with fake CRICOS codes`);
      
      // Delete associated intakes first
      const courseIds = corruptedCourses.map((course: any) => course.id);
      const { error: intakeDeleteError } = await supabase
        .from('intakes')
        .delete()
        .in('course_id', courseIds);

      if (intakeDeleteError) throw intakeDeleteError;

      // Delete corrupted courses
      const { error: courseDeleteError } = await supabase
        .from('courses')
        .delete()
        .in('id', courseIds);

      if (courseDeleteError) throw courseDeleteError;

      console.log(`✅ Cleared ${corruptedCourses.length} corrupted courses`);
    }
  } catch (error) {
    console.error('Error clearing corrupted data:', error);
    throw error;
  }
}

async function importSheetsData(sheetsData: any, supabase: any) {
  const stats = {
    faculties: { created: 0, existing: 0, names: [] as string[] },
    courses: { created: 0, existing: 0, updated: 0, createdList: [] as string[], existingList: [] as string[], errors: [] as string[] },
    skipped: { invalidCricos: 0, duplicates: 0 },
    sheets: { processed: 0, names: [] as string[] }
  };

  try {
    // Process each sheet from Google Sheets
    for (const [sheetName, sheetRows] of Object.entries(sheetsData)) {
      if (!Array.isArray(sheetRows)) {
        stats.courses.errors.push(`Sheet '${sheetName}' does not contain an array of courses`);
        continue;
      }

      stats.sheets.processed++;
      stats.sheets.names.push(sheetName);
      console.log(`Processing sheet: ${sheetName} with ${sheetRows.length} courses`);

      // Process each row from current sheet
      for (const row of sheetRows) {
        const { Faculty: facultyName, 'Course Name': courseName, 'VET Code': vetCode, 'CRICOS Code': cricosCode } = row;

        // Skip rows with missing essential data
        if (!facultyName || !courseName || !cricosCode) {
          stats.courses.errors.push(`Sheet '${sheetName}' - Skipped row: Missing essential data - Faculty: ${facultyName}, Course: ${courseName}, CRICOS: ${cricosCode}`);
          continue;
        }

        // Skip rows with fake CRICOS codes
        if (cricosCode.startsWith('CR') || cricosCode.length > 10) {
          stats.skipped.invalidCricos++;
          stats.courses.errors.push(`Sheet '${sheetName}' - Skipped course '${courseName}': Invalid CRICOS code '${cricosCode}'`);
          continue;
        }

        // Handle faculty
        const { data: existingFaculty } = await supabase
          .from('faculties')
          .select('id')
          .eq('name', facultyName)
          .single();

        let facultyId;
        if (existingFaculty) {
          facultyId = existingFaculty.id;
          if (!stats.faculties.names.includes(`${facultyName} (existing)`)) {
            stats.faculties.existing++;
            stats.faculties.names.push(`${facultyName} (existing)`);
          }
        } else {
          // Create new faculty
          const { data: newFaculty, error } = await supabase
            .from('faculties')
            .insert({ name: facultyName })
            .select('id')
            .single();

          if (error) throw error;
          facultyId = newFaculty.id;
          stats.faculties.created++;
          stats.faculties.names.push(`${facultyName} (created)`);
        }

        // Handle course
        const { data: existingCourse } = await supabase
          .from('courses')
          .select('id, cricos_code')
          .eq('course_name', courseName)
          .eq('faculty_id', facultyId)
          .single();

        if (existingCourse) {
          // Update existing course with clean data
          const { error: updateError } = await supabase
            .from('courses')
            .update({
              cricos_code: cricosCode,
              vet_code: vetCode || null,
              updated_at: new Date().toISOString()
            })
            .eq('id', existingCourse.id);

          if (updateError) throw updateError;
          
          stats.courses.updated++;
          stats.courses.existingList.push(`${courseName} (${facultyName}/${sheetName}) - Updated CRICOS: ${cricosCode}`);
        } else {
          // Create new course with clean data
          const { error: insertError } = await supabase
            .from('courses')
            .insert({
              course_name: courseName,
              faculty_id: facultyId,
              cricos_code: cricosCode,
              vet_code: vetCode || null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (insertError) {
            // Check if it's a duplicate CRICOS code error
            if (insertError.code === '23505' && insertError.message.includes('cricos_code')) {
              stats.skipped.duplicates++;
              stats.courses.errors.push(`Sheet '${sheetName}' - Skipped course '${courseName}': Duplicate CRICOS code '${cricosCode}'`);
              continue;
            }
            throw insertError;
          }

          stats.courses.created++;
          stats.courses.createdList.push(`${courseName} (${facultyName}/${sheetName}) - CRICOS: ${cricosCode}`);
        }
      }
    }

    return stats;
  } catch (error) {
    console.error('Google Sheets import error:', error);
    throw error;
  }
}
# Supabase Authentication System

This project uses Supabase for authentication. The authentication system is set up to allow only admin users to log in (no public sign-up).

## Authentication Flow

1. Users visit the login page (`/login`)
2. They enter their email and password
3. If valid, they are redirected to the main application
4. Protected routes are secured via middleware
5. User session is maintained via Supabase Auth

## Admin User Credentials

An admin user has been created. Contact your system administrator for login credentials.

## Authentication Files

- `lib/supabase/auth.ts` - Core authentication functions
- `contexts/AuthContext.tsx` - React context for authentication state
- `components/LoginForm.tsx` - Login form component
- `components/UserProfile.tsx` - User profile component with logout functionality
- `middleware.ts` - Route protection middleware

## How to Add More Users

To add more users, you can use the Supabase dashboard or run SQL queries through the MCP:

```sql
-- Create a new user
INSERT INTO auth.users (
  instance_id,
  id,
  aud,
  role,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_app_meta_data,
  raw_user_meta_data
)
VALUES (
  '00000000-0000-0000-0000-000000000000',
  gen_random_uuid(),
  'authenticated',
  'authenticated',
  '<EMAIL>',
  crypt('SecurePassword123!', gen_salt('bf')),
  now(),
  now(),
  now(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "New User"}'
)
```

## Security Considerations

- Passwords are securely hashed in the database
- Authentication state is managed securely via Supabase Auth
- Protected routes are secured via middleware
- No public sign-up is allowed
- Session timeout is set to 24 hours

## Customization

To customize the authentication system:

1. Update the login form UI in `components/LoginForm.tsx`
2. Modify the authentication context in `contexts/AuthContext.tsx`
3. Adjust route protection rules in `middleware.ts`
4. Update user profile display in `components/UserProfile.tsx`

## Troubleshooting

If you encounter authentication issues:

1. Check that Supabase is properly configured in `.env.local`
2. Verify that the user exists in the Supabase Auth tables
3. Check browser console for any errors
4. Ensure the middleware is correctly configured

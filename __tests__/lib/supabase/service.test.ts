// Mock the Supabase client
jest.mock('@/lib/supabase/client', () => {
  return {
    supabase: {
      from: jest.fn(),
    },
    Provider: {},
    Faculty: {},
    Course: {},
    Location: {},
    Intake: {},
    LegacyCollege: {},
    LegacyCourse: {},
  };
});

import { SupabaseService } from '@/lib/supabase/service';
import { supabase } from '@/lib/supabase/client';

// Get the mocked supabase client
const mockedSupabase = supabase as jest.Mocked<typeof supabase>;

describe('SupabaseService', () => {
  let service: SupabaseService;

  beforeEach(() => {
    service = new SupabaseService();
    jest.clearAllMocks();
  });

  describe('getAllProviders', () => {
    it('should fetch all providers successfully', async () => {
      const mockData = [
        { id: '1', name: 'Provider 1', created_at: '2024-01-01' },
        { id: '2', name: 'Provider 2', created_at: '2024-01-02' },
      ];

      mockedSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          order: jest.fn().mockResolvedValue({
            data: mockData,
            error: null,
          }),
        }),
      } as any);

      const result = await service.getAllProviders();

      expect(mockedSupabase.from).toHaveBeenCalledWith('providers');
      expect(result).toEqual(mockData);
    });

    it('should handle errors when fetching providers', async () => {
      const mockError = { message: 'Database error' };

      mockedSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          order: jest.fn().mockResolvedValue({
            data: null,
            error: mockError,
          }),
        }),
      } as any);

      await expect(service.getAllProviders()).rejects.toThrow('Failed to fetch providers: Database error');
    });

    it('should return empty array when no data', async () => {
      mockedSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          order: jest.fn().mockResolvedValue({
            data: null,
            error: null,
          }),
        }),
      } as any);

      const result = await service.getAllProviders();
      expect(result).toEqual([]);
    });
  });

  describe('getFacultiesByProvider', () => {
    it('should fetch faculties for a specific provider', async () => {
      const providerId = 'provider-123';
      const mockData = [
        { id: '1', provider_id: providerId, name: 'Faculty 1', created_at: '2024-01-01' },
      ];

      mockedSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockData,
              error: null,
            }),
          }),
        }),
      } as any);

      const result = await service.getFacultiesByProvider(providerId);

      expect(mockedSupabase.from).toHaveBeenCalledWith('faculties');
      expect(result).toEqual(mockData);
    });
  });

  describe('getIntakesByCourse', () => {
    it('should fetch intakes for a specific course', async () => {
      const courseId = 'course-123';
      const mockData = [
        {
          id: '1',
          course_id: courseId,
          location_id: 'loc-1',
          intake_date: '2024-02-01',
          created_at: '2024-01-01',
          location: { id: 'loc-1', name: 'Sydney', code: 'SYD' },
        },
      ];

      mockedSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockData,
              error: null,
            }),
          }),
        }),
      } as any);

      const result = await service.getIntakesByCourse(courseId);

      expect(mockedSupabase.from).toHaveBeenCalledWith('intakes');
      expect(result).toEqual(mockData);
    });
  });

  describe('getNextAvailableIntakes', () => {
    it('should fetch next available intakes after a specific date', async () => {
      const courseId = 'course-123';
      const afterDate = '2024-01-15';
      const mockData = [
        {
          id: '1',
          course_id: courseId,
          location_id: 'loc-1',
          intake_date: '2024-02-01',
          created_at: '2024-01-01',
        },
      ];

      mockedSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            gte: jest.fn().mockReturnValue({
              order: jest.fn().mockReturnValue({
                limit: jest.fn().mockResolvedValue({
                  data: mockData,
                  error: null,
                }),
              }),
            }),
          }),
        }),
      } as any);

      const result = await service.getNextAvailableIntakes(courseId, afterDate);

      expect(mockedSupabase.from).toHaveBeenCalledWith('intakes');
      expect(result).toEqual(mockData);
    });
  });

  describe('searchCourses', () => {
    it('should search courses by term', async () => {
      const searchTerm = 'business';
      const mockData = [
        {
          id: '1',
          course_name: 'Business Administration',
          vet_code: 'BSB50120',
          cricos_code: '12345',
          faculty: { name: 'Business Faculty' },
        },
      ];

      mockedSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          or: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockData,
              error: null,
            }),
          }),
        }),
      } as any);

      const result = await service.searchCourses(searchTerm);

      expect(mockedSupabase.from).toHaveBeenCalledWith('courses');
      expect(result).toEqual(mockData);
    });
  });

  describe('getStats', () => {
    it('should fetch statistics for all tables', async () => {
      // Mock each table count query
      const mockCountResponse = { count: 10, error: null };

      mockedSupabase.from.mockReturnValue({
        select: jest.fn().mockResolvedValue(mockCountResponse),
      } as any);

      const result = await service.getStats();

      expect(result).toEqual({
        providers: 10,
        faculties: 10,
        courses: 10,
        locations: 10,
        intakes: 10,
      });
    });

    it('should handle errors in stats fetching', async () => {
      mockedSupabase.from.mockImplementation(() => {
        throw new Error('Database error');
      });

      const result = await service.getStats();

      expect(result).toEqual({
        providers: 0,
        faculties: 0,
        courses: 0,
        locations: 0,
        intakes: 0,
      });
    });
  });

  describe('generateSampleIntakes', () => {
    it('should generate sample intake dates when no data exists', async () => {
      // Mock empty providers
      mockedSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          order: jest.fn().mockResolvedValue({
            data: [],
            error: null,
          }),
        }),
      } as any);

      const result = await service.getLegacyFormat();
      expect(result).toEqual([]);
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      mockedSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          order: jest.fn().mockRejectedValue(new Error('Network error')),
        }),
      } as any);

      await expect(service.getAllProviders()).rejects.toThrow();
    });

    it('should handle malformed data gracefully', async () => {
      mockedSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          order: jest.fn().mockResolvedValue({
            data: [{ invalid: 'data' }],
            error: null,
          }),
        }),
      } as any);

      const result = await service.getAllProviders();
      expect(result).toEqual([{ invalid: 'data' }]);
    });
  });
});

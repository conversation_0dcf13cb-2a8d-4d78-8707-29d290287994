import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { DatePicker } from "@/components/ui/DatePicker";

// Mock Supabase client if needed
jest.mock("@/lib/supabase/client", () => {
  return {
    supabase: {
      from: jest.fn(),
    },
    Provider: {},
    Faculty: {},
    Course: {},
    Location: {},
    Intake: {},
    LegacyCollege: {},
    LegacyCourse: {},
  };
});

describe("DatePicker", () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it("renders input field with placeholder", () => {
    render(<DatePicker value="" onChange={mockOnChange} />);

    const input = screen.getByPlaceholderText("DD/MM/YYYY");
    expect(input).toBeInTheDocument();
  });

  it("renders calendar icon", () => {
    render(<DatePicker value="" onChange={mockOnChange} />);

    const calendarButton = screen.getByRole("button", {
      name: /open calendar/i,
    });
    expect(calendarButton).toBeInTheDocument();

    // Check that the SVG icon is present within the button
    const calendarIcon = calendarButton.querySelector("svg");
    expect(calendarIcon).toBeInTheDocument();
  });

  it("renders native date input", () => {
    render(<DatePicker value="" onChange={mockOnChange} />);

    const dateInputs = screen.getAllByDisplayValue("");
    const nativeDateInput = dateInputs.find(
      (input) => input.getAttribute("type") === "date"
    );
    expect(nativeDateInput).toBeInTheDocument();
    expect(nativeDateInput).toHaveAttribute("type", "date");
  });

  it("calls onChange when date is selected via native input", () => {
    render(<DatePicker value="" onChange={mockOnChange} />);

    const dateInputs = screen.getAllByDisplayValue("");
    const nativeDateInput = dateInputs.find(
      (input) => input.getAttribute("type") === "date"
    );
    fireEvent.change(nativeDateInput!, { target: { value: "2025-01-15" } });

    expect(mockOnChange).toHaveBeenCalledWith("2025-01-15");
  });

  it("formats input value correctly when provided", () => {
    render(<DatePicker value="2025-01-15" onChange={mockOnChange} />);

    const input = screen.getByDisplayValue("15/01/2025");
    expect(input).toBeInTheDocument();
  });

  it("handles disabled state", () => {
    render(<DatePicker value="" onChange={mockOnChange} disabled />);

    const dateInputs = screen.getAllByDisplayValue("");
    const nativeDateInput = dateInputs.find(
      (input) => input.getAttribute("type") === "date"
    );
    expect(nativeDateInput).toBeDisabled();
  });

  it("displays empty placeholder when no value", () => {
    render(<DatePicker value="" onChange={mockOnChange} />);

    const input = screen.getByPlaceholderText("DD/MM/YYYY");
    expect(input).toHaveValue("");
  });

  it("updates display when value changes", () => {
    const { rerender } = render(
      <DatePicker value="" onChange={mockOnChange} />
    );

    rerender(<DatePicker value="2025-12-25" onChange={mockOnChange} />);

    const input = screen.getByDisplayValue("25/12/2025");
    expect(input).toBeInTheDocument();
  });
});

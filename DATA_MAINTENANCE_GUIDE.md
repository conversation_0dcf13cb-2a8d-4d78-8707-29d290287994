# 📊 Student Deferment Calculator - Data Maintenance Guide

## 🎯 **Current Data Status**
- **6 Colleges** with **58 Courses** and **1,326 Intake Dates**
- Data successfully cleaned and validated ✅
- All dates formatted consistently (YYYY-MM-DD)
- Duplicates removed, chronologically sorted

---

## 🚀 **Quick Update Methods**

### **Method 1: CSV Import/Export (Recommended for Bulk Updates)**

#### Export Current Data to CSV:
```bash
# Using the built-in data manager
node -e "
const manager = require('./lib/data/dataManager.js');
const fs = require('fs');
async function exportCSV() {
  const instance = manager.default.getInstance();
  await instance.loadData('./intake-dates.json');
  const csv = instance.exportAsCSV();
  fs.writeFileSync('intake-dates.csv', csv);
  console.log('✅ Data exported to intake-dates.csv');
}
exportCSV();
"
```

#### Edit CSV in Excel/Google Sheets:
- Open `intake-dates.csv` in your preferred spreadsheet application
- Columns: `College`, `Course`, `Intake Date`
- Make your changes (add/edit/remove rows)
- Save as CSV

#### Import Updated CSV:
```bash
node scripts/csvToJson.js intake-dates.csv intake-dates.json
```

### **Method 2: Direct JSON Editing**

#### Validate Before Use:
```bash
node -e "
const data = require('./intake-dates.json');
console.log('✅ JSON is valid');
console.log(\`📊 \${data.length} colleges, \${data.reduce((s,c) => s + c.courses.length, 0)} courses\`);
"
```

---

## 🛠 **Maintenance Scripts**

### **Available Tools:**

| Script | Purpose | Usage |
|--------|---------|-------|
| `scripts/parseIntakeData.py` | Clean malformed JSON data | `python3 scripts/parseIntakeData.py` |
| `scripts/csvToJson.js` | Convert CSV back to JSON | `node scripts/csvToJson.js input.csv output.json` |
| `lib/data/dataManager.ts` | Runtime validation & utilities | Import in React components |

### **Data Validation:**
```bash
# Check for issues
node -e "
const manager = require('./lib/data/dataManager.js');
async function validate() {
  const instance = manager.default.getInstance();
  const result = await instance.loadData('./intake-dates.json');
  console.log(\`Valid: \${result.isValid}\`);
  if (result.errors.length) console.log('❌ Errors:', result.errors);
  if (result.warnings.length) console.log('⚠️ Warnings:', result.warnings);
}
validate();
"
```

---

## 📋 **Step-by-Step Update Process**

### **For Regular Updates (Adding New Intake Dates):**

1. **Export to CSV:**
   ```bash
   npm run export-csv
   ```

2. **Edit in Excel/Google Sheets:**
   - Add new rows with: College Name, Course Name, Date (YYYY-MM-DD)
   - Sort by College → Course → Date

3. **Import Back:**
   ```bash
   npm run import-csv
   ```

4. **Validate:**
   ```bash
   npm run validate-data
   ```

5. **Test Application:**
   ```bash
   npm run dev
   ```

### **For Structural Changes (New Colleges/Courses):**

1. **Backup Current Data:**
   ```bash
   cp intake-dates.json intake-dates-backup-$(date +%Y%m%d).json
   ```

2. **Edit JSON Directly or Use CSV Method**

3. **Validate Structure:**
   ```bash
   python3 scripts/parseIntakeData.py
   ```

---

## 📅 **Regular Maintenance Schedule**

### **Monthly (Recommended):**
- Review and add new intake dates
- Remove past dates (optional - app handles this automatically)
- Validate data integrity

### **Quarterly:**
- Review course names for accuracy
- Check for new colleges/campuses
- Update course codes if changed

### **Annually:**
- Archive old data
- Plan for next year's intake cycles
- Review and optimize data structure

---

## 🔧 **Quick Commands (Add to package.json)**

```json
{
  "scripts": {
    "export-csv": "node -e \"const m=require('./lib/data/dataManager.js');const fs=require('fs');(async()=>{const i=m.default.getInstance();await i.loadData('./intake-dates.json');fs.writeFileSync('intake-dates.csv',i.exportAsCSV());console.log('✅ Exported to CSV');})()\"",
    "import-csv": "node scripts/csvToJson.js intake-dates.csv intake-dates.json",
    "validate-data": "python3 scripts/parseIntakeData.py",
    "backup-data": "cp intake-dates.json intake-dates-backup-$(date +%Y%m%d).json",
    "data-stats": "node -e \"const d=require('./intake-dates.json');console.log(\`📊 ${d.length} colleges, ${d.reduce((s,c)=>s+c.courses.length,0)} courses, ${d.reduce((s,c)=>s+c.courses.reduce((cs,cr)=>cs+cr.intakes.length,0),0)} intakes\`);\""
  }
}
```

---

## 🚨 **Troubleshooting**

### **Common Issues:**

#### "JSON Parse Error"
```bash
# Fix with Python script
python3 scripts/parseIntakeData.py
```

#### "Missing Dates"
```bash
# Validate and show issues
npm run validate-data
```

#### "Duplicate Entries"
```bash
# Automatic deduplication
python3 scripts/parseIntakeData.py
```

---

## 📊 **Data Structure Reference**

```typescript
interface College {
  collegeName: string;
  courses: Course[];
}

interface Course {
  courseName: string;
  intakes: IntakeDate[];
}

interface IntakeDate {
  date: string; // YYYY-MM-DD format
}
```

### **Example:**
```json
[
  {
    "collegeName": "AIBT",
    "courses": [
      {
        "courseName": "BSB80120 Graduate Diploma of Management (Learning) (SYD)",
        "intakes": [
          { "date": "2025-05-12" },
          { "date": "2025-06-02" },
          { "date": "2025-07-21" }
        ]
      }
    ]
  }
]
```

---

## 🎯 **Future Enhancements**

### **Immediate Improvements:**
- [ ] Add web-based admin interface for non-technical users
- [ ] Implement automatic backup before changes
- [ ] Add bulk import validation with preview

### **Advanced Features:**
- [ ] Database migration (SQLite → PostgreSQL)
- [ ] REST API for data management
- [ ] Real-time sync with external systems
- [ ] Automated intake date generation based on patterns

---

## 📞 **Support**

For technical issues:
1. Check the `data-report.txt` for current data statistics
2. Review validation errors with `npm run validate-data`
3. Use the backup files in case of corruption
4. Refer to this guide for step-by-step solutions

**Remember:** Always backup your data before making significant changes!

---

*Last Updated: $(date)*
*Data Status: ✅ Clean and Validated*

{"name": "edupace", "version": "0.1.0", "private": true, "description": "EduPace - Comprehensive educational platform with tools for course management, deferment calculations, intake dates, and academic planning", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.51.0", "@tailwindcss/forms": "^0.5.10", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "lucide-react": "^0.469.0", "next": "14.2.19", "ogl": "^1.0.11", "postcss": "^8.4.49", "postprocessing": "^6.37.6", "react": "^18", "react-day-picker": "^9.8.0", "react-dom": "^18", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.1", "three": "^0.167.1", "typescript": "^5"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/three": "^0.178.1", "eslint": "^8", "eslint-config-next": "14.2.19", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4"}}
/** @type {import('next').NextConfig} */
const nextConfig = {
  // App Router is now the default in Next.js 14, so we don't need to enable it explicitly

  // Development cache busting
  async headers() {
    if (process.env.NODE_ENV === "development") {
      return [
        {
          source: "/(.*)",
          headers: [
            {
              key: "Cache-Control",
              value: "no-cache, no-store, must-revalidate",
            },
            {
              key: "Pragma",
              value: "no-cache",
            },
            {
              key: "Expires",
              value: "0",
            },
          ],
        },
      ];
    }
    return [];
  },
};

module.exports = nextConfig

# CT Calculator Database Setup

## 🗄️ Database Table Creation

To enable the save calculation feature, you need to create the `ct_calculations` table in your Supabase database.

### Step 1: Access Supabase SQL Editor

1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor** in the left sidebar
3. Click **New Query**

### Step 2: Run the Migration

Copy and paste the contents of `migrations/create_ct_calculations_table.sql` into the SQL editor and run it.

Or copy this SQL directly:

```sql
-- Create CT Calculations table for storing Credit Transfer calculations
CREATE TABLE IF NOT EXISTS ct_calculations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Calculation details
  workflow_type VARCHAR(50) NOT NULL,
  student_name VARCHAR(255),
  course_name VARCHAR(255),
  original_fee DECIMAL(10,2) NOT NULL,
  new_fee DECIMAL(10,2) NOT NULL,
  savings DECIMAL(10,2) NOT NULL,
  formula TEXT NOT NULL,
  units_reduction INTEGER DEFAULT 0,
  
  -- Additional data
  notes TEXT,
  calculation_data JSONB,
  
  -- Constraints
  CONSTRAINT ct_calculations_positive_fees CHECK (original_fee >= 0 AND new_fee >= 0),
  CONSTRAINT ct_calculations_valid_savings CHECK (savings >= 0)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ct_calculations_user_id ON ct_calculations(user_id);
CREATE INDEX IF NOT EXISTS idx_ct_calculations_created_at ON ct_calculations(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ct_calculations_workflow_type ON ct_calculations(workflow_type);
CREATE INDEX IF NOT EXISTS idx_ct_calculations_student_name ON ct_calculations(student_name);

-- Enable Row Level Security (RLS)
ALTER TABLE ct_calculations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view own ct_calculations" ON ct_calculations
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own ct_calculations" ON ct_calculations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own ct_calculations" ON ct_calculations
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own ct_calculations" ON ct_calculations
  FOR DELETE USING (auth.uid() = user_id);

-- Create auto-update trigger
CREATE OR REPLACE FUNCTION update_ct_calculations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_ct_calculations_updated_at
  BEFORE UPDATE ON ct_calculations
  FOR EACH ROW
  EXECUTE FUNCTION update_ct_calculations_updated_at();

-- Grant permissions
GRANT ALL ON ct_calculations TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;
```

### Step 3: Verify Table Creation

Run this query to verify the table was created successfully:

```sql
SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'ct_calculations'
ORDER BY ordinal_position;
```

## ✅ Features Enabled

Once the table is created, the following features will work:

### Save Calculation
- Click the **Save** button in the CT Calculator
- Enter a custom title for your calculation
- Calculation is saved to the database with full details

### Data Stored
- **Basic Info**: Title, workflow type, student/course names
- **Financial Data**: Original fee, new fee, savings amount
- **Calculation Details**: Formula used, units reduction
- **Full Calculation**: Complete calculation object as JSON
- **Metadata**: Creation/update timestamps, user association

### Security
- **Row Level Security**: Users can only see their own calculations
- **User Association**: Calculations are linked to authenticated users
- **Data Validation**: Constraints ensure data integrity

## 🔧 Usage

1. **Complete a CT calculation** in the chat interface
2. **Click the Save button** in the results section
3. **Enter a title** when prompted
4. **Calculation is saved** and can be accessed later

## 📊 Future Enhancements

The database structure supports future features like:
- Admin dashboard to view all calculations
- Calculation history and search
- Export/import functionality
- Reporting and analytics
- Bulk operations

## 🚨 Important Notes

- **Authentication Required**: Users must be logged in to save calculations
- **RLS Enabled**: Each user can only access their own calculations
- **Data Integrity**: Constraints prevent invalid financial data
- **Auto-timestamps**: Creation and update times are automatically managed

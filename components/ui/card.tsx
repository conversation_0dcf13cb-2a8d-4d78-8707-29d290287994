import * as React from "react"
import { cn } from "@/lib/utils"

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "glass" | "gradient" | "elevated";
  padding?: "none" | "sm" | "default" | "lg" | "xl";
  hover?: boolean;
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  (
    {
      className,
      variant = "default",
      padding = "default",
      hover = true,
      ...props
    },
    ref
  ) => {
    return (
      <div
        ref={ref}
        className={cn(
          // Base styles
          "relative rounded-2xl border transition-all duration-500 ease-out",

          // Variant styles
          {
            // Default - Clean white card
            "bg-white border-gray-200 shadow-sm": variant === "default",

            // Glass - Glass morphism effect
            "bg-white/10 backdrop-blur-xl border-white/20 shadow-glass":
              variant === "glass",

            // Gradient - Subtle gradient background
            "bg-gradient-to-br from-white to-gray-50 border-gray-200 shadow-lg":
              variant === "gradient",

            // Elevated - Strong shadow
            "bg-white border-gray-200 shadow-xl": variant === "elevated",
          },

          // Hover effects
          hover && {
            "hover:-translate-y-2 hover:shadow-xl": variant === "default",
            "hover:-translate-y-2 hover:shadow-2xl hover:bg-white/15":
              variant === "glass",
            "hover:-translate-y-2 hover:shadow-2xl":
              variant === "gradient" || variant === "elevated",
          },

          // Padding styles
          {
            "p-0": padding === "none",
            "p-4": padding === "sm",
            "p-6": padding === "default",
            "p-8": padding === "lg",
            "p-10": padding === "xl",
          },

          className
        )}
        {...props}
      />
    );
  }
);
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 pb-6", className)}
    {...props}
  />
));
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-2xl font-bold leading-none tracking-tight text-gray-900",
      className
    )}
    {...props}
  />
));
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-base text-gray-600 leading-relaxed", className)}
    {...props}
  />
));
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("", className)} {...props} />
));
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center pt-6", className)}
    {...props}
  />
));
CardFooter.displayName = "CardFooter"

export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardDescription,
  CardContent,
};

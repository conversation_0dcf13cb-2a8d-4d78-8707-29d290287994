import * as React from "react"
import { cn } from "@/lib/utils"

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?:
    | "primary"
    | "secondary"
    | "accent"
    | "outline"
    | "ghost"
    | "link"
    | "destructive";
  size?: "sm" | "default" | "lg" | "xl" | "icon";
  loading?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant = "primary",
      size = "default",
      loading = false,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    return (
      <button
        className={cn(
          // Base styles
          "relative inline-flex items-center justify-center gap-2 font-semibold transition-all duration-300 ease-out",
          "focus:outline-none focus:ring-4 focus:ring-offset-0 disabled:opacity-50 disabled:cursor-not-allowed",
          "disabled:hover:scale-100 disabled:hover:translate-y-0",

          // Variant styles
          {
            // Primary - Blue gradient
            "bg-gradient-to-r from-primary-600 to-primary-500 hover:from-primary-500 hover:to-primary-400 text-white shadow-lg hover:shadow-xl focus:ring-primary-500/50":
              variant === "primary",

            // Secondary - Glass morphism
            "bg-white/10 backdrop-blur-md hover:bg-white/20 text-white border border-white/20 hover:border-white/30 shadow-glass hover:shadow-xl focus:ring-white/50":
              variant === "secondary",

            // Accent - Green gradient
            "bg-gradient-to-r from-accent-600 to-accent-500 hover:from-accent-500 hover:to-accent-400 text-white shadow-lg hover:shadow-xl focus:ring-accent-500/50":
              variant === "accent",

            // Outline - Border with hover fill
            "border-2 border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white focus:ring-primary-500/50":
              variant === "outline",

            // Ghost - Minimal hover effect
            "text-gray-700 hover:text-primary-600 hover:bg-primary-50 focus:ring-primary-500/50":
              variant === "ghost",

            // Link - Text only
            "text-primary-600 hover:text-primary-700 underline-offset-4 hover:underline focus:ring-primary-500/50":
              variant === "link",

            // Destructive - Red gradient
            "bg-gradient-to-r from-error-600 to-error-500 hover:from-error-500 hover:to-error-400 text-white shadow-lg hover:shadow-xl focus:ring-error-500/50":
              variant === "destructive",
          },

          // Size styles
          {
            "px-3 py-1.5 text-sm rounded-lg": size === "sm",
            "px-6 py-3 text-base rounded-xl": size === "default",
            "px-8 py-4 text-lg rounded-2xl": size === "lg",
            "px-10 py-5 text-xl rounded-2xl": size === "xl",
            "p-3 rounded-xl": size === "icon",
          },

          className
        )}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
          </div>
        )}
        <span className={cn("flex items-center gap-2", loading && "opacity-0")}>
          {children}
        </span>
      </button>
    );
  }
);
Button.displayName = "Button"

export { Button }

"use client";

import React, { useState, useEffect, useRef } from "react";
import { Search, ChevronDown, Book } from "lucide-react";
import { supabaseService } from "@/lib/supabase/service";
import { Course } from "@/lib/supabase/client";
import { cn } from "@/lib/utils";

interface CourseSearchInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function CourseSearchInput({
  value,
  onChange,
  placeholder = "Search for courses...",
  className,
  disabled,
}: CourseSearchInputProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState(value);
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);

  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Search courses when search term changes
  useEffect(() => {
    const searchCourses = async () => {
      if (searchTerm.length < 2) {
        setCourses([]);
        return;
      }

      setLoading(true);
      try {
        const results = await supabaseService.searchCourses(searchTerm);
        setCourses(results.slice(0, 10)); // Limit to 10 results
      } catch (error) {
        console.error("Error searching courses:", error);
        setCourses([]);
      } finally {
        setLoading(false);
      }
    };

    const debounceTimer = setTimeout(searchCourses, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchTerm]);

  // Update search term when value prop changes
  useEffect(() => {
    setSearchTerm(value);
    // Try to find the selected course if value is set
    if (value && !selectedCourse) {
      supabaseService.searchCourses(value).then((results) => {
        const found = results.find((course) => course.course_name === value);
        if (found) {
          setSelectedCourse(found);
        }
      });
    }
  }, [value, selectedCourse]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);
    onChange(newValue);
    setSelectedCourse(null);
    setIsOpen(true);
  };

  const handleCourseSelect = (course: Course) => {
    setSearchTerm(course.course_name);
    onChange(course.course_name);
    setSelectedCourse(course);
    setIsOpen(false);
  };

  const handleInputFocus = () => {
    setIsOpen(true);
    if (searchTerm.length >= 2) {
      // Trigger search if we have enough characters
      supabaseService.searchCourses(searchTerm).then((results) => {
        setCourses(results.slice(0, 10));
      });
    }
  };

  return (
    <div ref={containerRef} className={cn("relative", className)}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={searchTerm}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            "flex w-full font-medium transition-all duration-300 ease-out",
            "focus:outline-none focus:ring-4 focus:ring-offset-0",
            "disabled:cursor-not-allowed disabled:opacity-50",
            "placeholder:text-gray-400",
            "bg-white border border-gray-300 text-gray-900",
            "focus:border-primary-500 focus:ring-primary-500/20",
            "px-4 py-3 text-base rounded-xl pr-10"
          )}
        />

        <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1">
          {loading && (
            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
          )}
          <Search className="w-4 h-4 text-gray-400" />
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && (courses.length > 0 || loading) && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-300 rounded-xl shadow-lg max-h-60 overflow-y-auto">
          {loading && (
            <div className="p-4 text-center text-gray-500">
              <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2" />
              Searching courses...
            </div>
          )}

          {!loading && courses.length === 0 && searchTerm.length >= 2 && (
            <div className="p-4 text-center text-gray-500">
              <Book className="w-8 h-8 mx-auto mb-2 text-gray-300" />
              <p>No courses found for &quot;{searchTerm}&quot;</p>
              <p className="text-sm">Try a different search term</p>
            </div>
          )}

          {!loading &&
            courses.map((course) => (
              <button
                key={course.id}
                onClick={() => handleCourseSelect(course)}
                className="w-full text-left p-4 hover:bg-blue-50 transition-colors border-b border-gray-100 last:border-b-0"
              >
                <div className="font-medium text-gray-900 mb-1">
                  {course.course_name}
                </div>
                <div className="text-sm text-gray-600 space-y-1">
                  {course.vet_code && <div>VET Code: {course.vet_code}</div>}
                  {course.cricos_code && (
                    <div>CRICOS: {course.cricos_code}</div>
                  )}
                  {course.faculty && (
                    <div className="text-blue-600">
                      {course.faculty.name}
                      {course.provider && ` - ${course.provider.name}`}
                    </div>
                  )}
                </div>
              </button>
            ))}
        </div>
      )}

      {/* Search hint */}
      {searchTerm.length > 0 && searchTerm.length < 2 && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-yellow-50 border border-yellow-200 rounded-xl p-3">
          <p className="text-sm text-yellow-700">
            Type at least 2 characters to search courses
          </p>
        </div>
      )}
    </div>
  );
}

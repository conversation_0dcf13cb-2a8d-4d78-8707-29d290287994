import * as React from "react";
import { cn } from "@/lib/utils";

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  variant?: "default" | "glass" | "outline";
  inputSize?: "sm" | "default" | "lg";
  error?: boolean;
  success?: boolean;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type,
      variant = "default",
      inputSize = "default",
      error = false,
      success = false,
      ...props
    },
    ref
  ) => {
    return (
      <input
        type={type}
        className={cn(
          // Base styles
          "flex w-full font-medium transition-all duration-300 ease-out",
          "focus:outline-none focus:ring-4 focus:ring-offset-0",
          "disabled:cursor-not-allowed disabled:opacity-50",
          "placeholder:text-gray-400",

          // Variant styles
          {
            // Default - Clean white input
            "bg-white border border-gray-300 text-gray-900 focus:border-primary-500 focus:ring-primary-500/20":
              variant === "default" && !error && !success,

            // Glass - Glass morphism effect
            "bg-white/10 backdrop-blur-md border border-white/20 text-white focus:border-white/40 focus:ring-white/20 placeholder:text-white/60":
              variant === "glass" && !error && !success,

            // Outline - Minimal border
            "bg-transparent border-2 border-gray-300 text-gray-900 focus:border-primary-500 focus:ring-primary-500/20":
              variant === "outline" && !error && !success,
          },

          // Size styles
          {
            "px-3 py-2 text-sm rounded-lg": inputSize === "sm",
            "px-4 py-3 text-base rounded-xl": inputSize === "default",
            "px-6 py-4 text-lg rounded-2xl": inputSize === "lg",
          },

          // Error state
          error && {
            "border-error-500 focus:border-error-500 focus:ring-error-500/20":
              variant === "default" || variant === "outline",
            "border-error-500/60 focus:border-error-500 focus:ring-error-500/20":
              variant === "glass",
          },

          // Success state
          success && {
            "border-success-500 focus:border-success-500 focus:ring-success-500/20":
              variant === "default" || variant === "outline",
            "border-success-500/60 focus:border-success-500 focus:ring-success-500/20":
              variant === "glass",
          },

          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
Input.displayName = "Input";

export { Input };

import * as React from "react";
import { cn } from "@/lib/utils";
import { ChevronDown } from "lucide-react";

export interface SelectProps
  extends React.SelectHTMLAttributes<HTMLSelectElement> {
  variant?: "default" | "glass" | "outline";
  selectSize?: "sm" | "default" | "lg";
  error?: boolean;
  success?: boolean;
}

const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
  (
    {
      className,
      variant = "default",
      selectSize = "default",
      error = false,
      success = false,
      children,
      ...props
    },
    ref
  ) => {
    return (
      <div className="relative">
        <select
          className={cn(
            // Base styles
            "flex w-full font-medium transition-all duration-300 ease-out appearance-none cursor-pointer",
            "focus:outline-none focus:ring-4 focus:ring-offset-0",
            "disabled:cursor-not-allowed disabled:opacity-50",

            // Variant styles
            {
              // Default - Clean white select
              "bg-white border border-gray-300 text-gray-900 focus:border-primary-500 focus:ring-primary-500/20":
                variant === "default" && !error && !success,

              // Glass - Glass morphism effect
              "bg-white/10 backdrop-blur-md border border-white/20 text-white focus:border-white/40 focus:ring-white/20":
                variant === "glass" && !error && !success,

              // Outline - Minimal border
              "bg-transparent border-2 border-gray-300 text-gray-900 focus:border-primary-500 focus:ring-primary-500/20":
                variant === "outline" && !error && !success,
            },

            // Size styles
            {
              "px-3 py-2 pr-8 text-sm rounded-lg": selectSize === "sm",
              "px-4 py-3 pr-10 text-base rounded-xl": selectSize === "default",
              "px-6 py-4 pr-12 text-lg rounded-2xl": selectSize === "lg",
            },

            // Error state
            error && {
              "border-error-500 focus:border-error-500 focus:ring-error-500/20":
                variant === "default" || variant === "outline",
              "border-error-500/60 focus:border-error-500 focus:ring-error-500/20":
                variant === "glass",
            },

            // Success state
            success && {
              "border-success-500 focus:border-success-500 focus:ring-success-500/20":
                variant === "default" || variant === "outline",
              "border-success-500/60 focus:border-success-500 focus:ring-success-500/20":
                variant === "glass",
            },

            className
          )}
          ref={ref}
          {...props}
        >
          {children}
        </select>

        {/* Custom dropdown arrow */}
        <div
          className={cn(
            "absolute inset-y-0 right-0 flex items-center pointer-events-none",
            {
              "pr-2": selectSize === "sm",
              "pr-3": selectSize === "default",
              "pr-4": selectSize === "lg",
            }
          )}
        >
          <ChevronDown
            className={cn(
              "text-gray-400",
              {
                "w-4 h-4": selectSize === "sm",
                "w-5 h-5": selectSize === "default",
                "w-6 h-6": selectSize === "lg",
              },
              variant === "glass" && "text-white/60"
            )}
          />
        </div>
      </div>
    );
  }
);
Select.displayName = "Select";

export { Select };

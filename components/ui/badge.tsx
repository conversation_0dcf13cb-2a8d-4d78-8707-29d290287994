import * as React from "react";
import { cn } from "@/lib/utils";

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?:
    | "default"
    | "primary"
    | "secondary"
    | "accent"
    | "success"
    | "warning"
    | "error"
    | "outline";
  size?: "sm" | "default" | "lg";
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant = "default", size = "default", ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          // Base styles
          "inline-flex items-center gap-1 font-semibold transition-all duration-200",

          // Variant styles
          {
            // Default - Gray
            "bg-gray-100 text-gray-800 border border-gray-200":
              variant === "default",

            // Primary - Blue
            "bg-primary-100 text-primary-800 border border-primary-200":
              variant === "primary",

            // Secondary - Indigo
            "bg-indigo-100 text-indigo-800 border border-indigo-200":
              variant === "secondary",

            // Accent - Green
            "bg-accent-100 text-accent-800 border border-accent-200":
              variant === "accent",

            // Success - Green
            "bg-success-100 text-success-800 border border-success-200":
              variant === "success",

            // Warning - Yellow
            "bg-warning-100 text-warning-800 border border-warning-200":
              variant === "warning",

            // Error - Red
            "bg-error-100 text-error-800 border border-error-200":
              variant === "error",

            // Outline - Transparent with border
            "bg-transparent text-gray-700 border-2 border-gray-300":
              variant === "outline",
          },

          // Size styles
          {
            "px-2 py-0.5 text-xs rounded-md": size === "sm",
            "px-3 py-1 text-sm rounded-lg": size === "default",
            "px-4 py-1.5 text-base rounded-xl": size === "lg",
          },

          className
        )}
        {...props}
      />
    );
  }
);
Badge.displayName = "Badge";

export { Badge };

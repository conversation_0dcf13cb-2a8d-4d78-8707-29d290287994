'use client';

import React, { useState, useEffect } from 'react';
import { Search, Filter, Calendar, Clock, MapPin, ChevronDown, ChevronRight, Building2, BookOpen } from 'lucide-react';
import { getProviders, getLocations } from "@/lib/data/courseData";
import { supabaseService } from "@/lib/supabase/service";
import type { Provider, Location } from "@/lib/supabase/client";

interface CourseWithLocations {
  id: string;
  courseName: string;
  providerName: string;
  locations: Array<{
    id: string;
    name: string;
    code?: string;
    intakes: Array<{ date: string }>;
  }>;
}

interface ProviderWithCourses {
  providerName: string;
  courses: CourseWithLocations[];
}

export default function IntakeDatesViewer() {
  const [coursesData, setCoursesData] = useState<CourseWithLocations[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [filteredProviders, setFilteredProviders] = useState<
    ProviderWithCourses[]
  >([]);
  const [selectedProvider, setSelectedProvider] = useState<string>("");
  const [selectedLocation, setSelectedLocation] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    providers: 0,
    faculties: 0,
    courses: 0,
    locations: 0,
    intakes: 0,
  });

  // Collapsible state management
  const [expandedProviders, setExpandedProviders] = useState<Set<string>>(
    new Set()
  );
  const [expandedCourses, setExpandedCourses] = useState<Set<string>>(
    new Set()
  );
  const [expandedLocations, setExpandedLocations] = useState<Set<string>>(
    new Set()
  );

  // Toggle functions for collapsible sections
  const toggleProvider = (providerName: string) => {
    const newExpanded = new Set(expandedProviders);
    if (newExpanded.has(providerName)) {
      newExpanded.delete(providerName);
    } else {
      newExpanded.add(providerName);
    }
    setExpandedProviders(newExpanded);
  };

  const toggleCourse = (courseId: string) => {
    const newExpanded = new Set(expandedCourses);
    if (newExpanded.has(courseId)) {
      newExpanded.delete(courseId);
    } else {
      newExpanded.add(courseId);
    }
    setExpandedCourses(newExpanded);
  };

  const toggleLocation = (locationKey: string) => {
    const newExpanded = new Set(expandedLocations);
    if (newExpanded.has(locationKey)) {
      newExpanded.delete(locationKey);
    } else {
      newExpanded.add(locationKey);
    }
    setExpandedLocations(newExpanded);
  };

  // Utility functions for expand/collapse all
  const expandAllProviders = () => {
    const allProviders = new Set(filteredProviders.map((p) => p.providerName));
    setExpandedProviders(allProviders);
  };

  const collapseAllProviders = () => {
    setExpandedProviders(new Set());
    setExpandedCourses(new Set());
    setExpandedLocations(new Set());
  };

  // Load all data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        const [providerData, locationData, statsData] = await Promise.all([
          getProviders(),
          getLocations(),
          supabaseService.getStats(),
        ]);

        // Load courses with proper location separation
        const coursesWithLocations =
          await supabaseService.getCoursesWithLocations();

        // Transform data to our new structure
        const transformedCourses: CourseWithLocations[] = [];

        for (const course of coursesWithLocations) {
          const courseLocations = [];

          for (const location of course.locations || []) {
            const intakes = await supabaseService.getIntakesByCourse(course.id);
            const locationIntakes = intakes
              .filter((intake) => intake.location?.id === location.id)
              .map((intake) => ({ date: intake.intake_date }));

            if (locationIntakes.length > 0) {
              courseLocations.push({
                id: location.id,
                name: location.name,
                code: location.code || undefined,
                intakes: locationIntakes,
              });
            }
          }

          if (courseLocations.length > 0) {
            transformedCourses.push({
              id: course.id,
              courseName: course.course_name,
              providerName: course.provider?.name || "Unknown Provider",
              locations: courseLocations,
            });
          }
        }

        setCoursesData(transformedCourses);
        setProviders(providerData);
        setLocations(locationData);
        setStats(statsData);
      } catch (error) {
        console.error("Failed to load data:", error);
      } finally {
        setIsLoading(false);
      }
    };
    loadData();
  }, []);

  // Group courses by provider and apply filters
  useEffect(() => {
    let filtered = coursesData;

    // Filter by selected provider
    if (selectedProvider) {
      filtered = filtered.filter(
        (course) => course.providerName === selectedProvider
      );
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter((course) =>
        course.courseName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by location
    if (selectedLocation) {
      filtered = filtered
        .map((course) => ({
          ...course,
          locations: course.locations.filter(
            (location) => location.id === selectedLocation
          ),
        }))
        .filter((course) => course.locations.length > 0);
    }

    // Group courses by provider
    const providerMap = new Map<string, CourseWithLocations[]>();

    filtered.forEach((course) => {
      if (!providerMap.has(course.providerName)) {
        providerMap.set(course.providerName, []);
      }
      providerMap.get(course.providerName)!.push(course);
    });

    // Convert to array and sort
    const groupedProviders: ProviderWithCourses[] = Array.from(
      providerMap.entries()
    )
      .map(([providerName, courses]) => ({
        providerName,
        courses: courses.sort((a, b) =>
          a.courseName.localeCompare(b.courseName)
        ),
      }))
      .sort((a, b) => a.providerName.localeCompare(b.providerName));

    setFilteredProviders(groupedProviders);
  }, [coursesData, selectedProvider, selectedLocation, searchTerm]);

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-8 border border-gray-200">
        <div className="text-center">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Clock className="w-6 h-6 text-blue-600 animate-spin" />
            <p className="text-gray-700 text-lg">Loading intake dates...</p>
          </div>
          <div className="space-y-3">
            <div className="bg-gray-200 rounded h-4 w-full animate-pulse"></div>
            <div className="bg-gray-200 rounded h-4 w-3/4 mx-auto animate-pulse"></div>
            <div className="bg-gray-200 rounded h-4 w-1/2 mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  const totalCourses = filteredProviders.reduce(
    (sum, provider) => sum + provider.courses.length,
    0
  );
  const totalIntakes = filteredProviders.reduce(
    (sum, provider) =>
      sum +
      provider.courses.reduce(
        (courseSum, course) =>
          courseSum +
          course.locations.reduce(
            (locationSum, location) => locationSum + location.intakes.length,
            0
          ),
        0
      ),
    0
  );

  return (
    <div className="space-y-8">
      {/* Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div className="bg-white rounded-lg p-4 text-center border border-gray-200 shadow-sm">
          <div className="text-2xl font-bold text-blue-600">
            {stats.providers}
          </div>
          <div className="text-sm text-gray-600">Providers</div>
        </div>
        <div className="bg-white rounded-lg p-4 text-center border border-gray-200 shadow-sm">
          <div className="text-2xl font-bold text-green-600">
            {stats.courses}
          </div>
          <div className="text-sm text-gray-600">Courses</div>
        </div>
        <div className="bg-white rounded-lg p-4 text-center border border-gray-200 shadow-sm">
          <div className="text-2xl font-bold text-purple-600">
            {stats.locations}
          </div>
          <div className="text-sm text-gray-600">Locations</div>
        </div>
        <div className="bg-white rounded-lg p-4 text-center border border-gray-200 shadow-sm">
          <div className="text-2xl font-bold text-yellow-600">
            {totalCourses}
          </div>
          <div className="text-sm text-gray-600">Filtered Courses</div>
        </div>
        <div className="bg-white rounded-lg p-4 text-center border border-gray-200 shadow-sm">
          <div className="text-2xl font-bold text-red-600">{totalIntakes}</div>
          <div className="text-sm text-gray-600">Intake Dates</div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-lg p-6 border border-gray-200">
        <div className="flex items-center gap-2 mb-4">
          <Filter className="w-5 h-5 text-blue-600" />
          <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* College Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Filter by Provider
            </label>
            <select
              value={selectedProvider}
              onChange={(e) => setSelectedProvider(e.target.value)}
              className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none"
            >
              <option value="">All Providers</option>
              {providers.map((provider) => (
                <option key={provider.id} value={provider.name}>
                  {provider.name}
                </option>
              ))}
            </select>
          </div>

          {/* Location Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Filter by Location
            </label>
            <select
              value={selectedLocation}
              onChange={(e) => setSelectedLocation(e.target.value)}
              className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none"
            >
              <option value="">All Locations</option>
              {locations.map((location) => (
                <option key={location.id} value={location.id}>
                  {location.name} {location.code && `(${location.code})`}
                </option>
              ))}
            </select>
          </div>

          {/* Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search Courses
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search course names..."
                className="w-full pl-10 pr-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none placeholder-gray-400"
              />
            </div>
          </div>
        </div>

        {/* Clear Filters & Expand/Collapse Controls */}
        <div className="mt-4 flex flex-wrap gap-3">
          {(selectedProvider || selectedLocation || searchTerm) && (
            <button
              onClick={() => {
                setSelectedProvider("");
                setSelectedLocation("");
                setSearchTerm("");
              }}
              className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors"
            >
              Clear All Filters
            </button>
          )}

          {filteredProviders.length > 0 && (
            <>
              <button
                onClick={expandAllProviders}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center gap-2"
              >
                <ChevronDown className="w-4 h-4" />
                Expand All
              </button>
              <button
                onClick={collapseAllProviders}
                className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors flex items-center gap-2"
              >
                <ChevronRight className="w-4 h-4" />
                Collapse All
              </button>
            </>
          )}
        </div>
      </div>

      {/* Results */}
      <div className="space-y-8">
        {filteredProviders.length === 0 ? (
          <div className="text-center py-12">
            <Calendar className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-400 mb-2">
              No Results Found
            </h3>
            <p className="text-gray-500">
              Try adjusting your filters or search terms to find intake dates.
            </p>
          </div>
        ) : (
          filteredProviders.map((provider) => {
            const isProviderExpanded = expandedProviders.has(
              provider.providerName
            );

            return (
              <div
                key={provider.providerName}
                className="bg-gray-800 rounded-lg overflow-hidden"
              >
                {/* Provider Header - Clickable */}
                <button
                  onClick={() => toggleProvider(provider.providerName)}
                  className="w-full flex items-center gap-3 p-6 hover:bg-gray-750 transition-colors text-left"
                >
                  {isProviderExpanded ? (
                    <ChevronDown className="w-5 h-5 text-blue-400 flex-shrink-0" />
                  ) : (
                    <ChevronRight className="w-5 h-5 text-blue-400 flex-shrink-0" />
                  )}
                  <Building2 className="w-5 h-5 text-blue-400 flex-shrink-0" />
                  <h2 className="text-2xl font-bold text-white">
                    {provider.providerName}
                  </h2>
                  <span className="text-sm text-gray-400">
                    ({provider.courses.length} course
                    {provider.courses.length !== 1 ? "s" : ""})
                  </span>
                </button>

                {/* Courses under this provider - Collapsible */}
                {isProviderExpanded && (
                  <div className="px-6 pb-6 space-y-4">
                    {provider.courses.map((course) => {
                      const isCourseExpanded = expandedCourses.has(course.id);

                      return (
                        <div
                          key={course.id}
                          className="border-l-2 border-gray-600 pl-4"
                        >
                          {/* Course Header - Clickable */}
                          <button
                            onClick={() => toggleCourse(course.id)}
                            className="w-full flex items-center gap-3 p-3 hover:bg-gray-700 rounded-lg transition-colors text-left"
                          >
                            {isCourseExpanded ? (
                              <ChevronDown className="w-4 h-4 text-green-400 flex-shrink-0" />
                            ) : (
                              <ChevronRight className="w-4 h-4 text-green-400 flex-shrink-0" />
                            )}
                            <BookOpen className="w-4 h-4 text-green-400 flex-shrink-0" />
                            <h3 className="text-lg font-semibold text-gray-100 flex-1">
                              {course.courseName}
                            </h3>
                            <span className="text-sm text-gray-400">
                              {course.locations.length} location
                              {course.locations.length !== 1 ? "s" : ""}
                            </span>
                          </button>

                          {/* Locations for this course - Collapsible */}
                          {isCourseExpanded && (
                            <div className="ml-6 mt-3 space-y-3">
                              {course.locations.map(
                                (location, locationIndex) => {
                                  const locationKey = `${course.id}-${location.id}`;
                                  const isLocationExpanded =
                                    expandedLocations.has(locationKey);

                                  return (
                                    <div
                                      key={`${location.id}-${locationIndex}`}
                                      className="border-l-2 border-gray-700 pl-4"
                                    >
                                      {/* Location Header - Clickable */}
                                      <button
                                        onClick={() =>
                                          toggleLocation(locationKey)
                                        }
                                        className="w-full flex items-center gap-2 p-2 hover:bg-gray-700 rounded-lg transition-colors text-left"
                                      >
                                        {isLocationExpanded ? (
                                          <ChevronDown className="w-4 h-4 text-yellow-400 flex-shrink-0" />
                                        ) : (
                                          <ChevronRight className="w-4 h-4 text-yellow-400 flex-shrink-0" />
                                        )}
                                        <MapPin className="w-4 h-4 text-yellow-400 flex-shrink-0" />
                                        <h4 className="text-base font-medium text-gray-200 flex-1">
                                          {location.name}{" "}
                                          {location.code &&
                                            `(${location.code})`}
                                        </h4>
                                        <span className="text-sm text-gray-400">
                                          {location.intakes.length} intake
                                          {location.intakes.length !== 1
                                            ? "s"
                                            : ""}
                                        </span>
                                      </button>

                                      {/* Intake Dates - Collapsible */}
                                      {isLocationExpanded && (
                                        <div className="ml-6 mt-3">
                                          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
                                            {location.intakes.map(
                                              (intake, intakeIndex) => (
                                                <div
                                                  key={`${intake.date}-${intakeIndex}`}
                                                  className="bg-gray-700 rounded px-3 py-2 text-center hover:bg-gray-600 transition-colors cursor-pointer"
                                                  title={`Click to select ${intake.date}`}
                                                >
                                                  <div className="text-sm font-medium text-white">
                                                    {new Date(
                                                      intake.date
                                                    ).toLocaleDateString(
                                                      "en-AU",
                                                      {
                                                        day: "2-digit",
                                                        month: "short",
                                                        year: "numeric",
                                                      }
                                                    )}
                                                  </div>
                                                  <div className="text-xs text-gray-400">
                                                    {new Date(
                                                      intake.date
                                                    ).toLocaleDateString(
                                                      "en-AU",
                                                      {
                                                        weekday: "short",
                                                      }
                                                    )}
                                                  </div>
                                                </div>
                                              )
                                            )}
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  );
                                }
                              )}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })
        )}
      </div>
    </div>
  );
}

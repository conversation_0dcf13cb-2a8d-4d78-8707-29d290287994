'use client';

import { Palette } from 'lucide-react';
import { useSidebarTheme } from '@/contexts/SidebarThemeContext';

export default function ThemeToggle() {
  const { currentTheme, nextTheme } = useSidebarTheme();

  return (
    <div className="mt-3">
      <button
        onClick={nextTheme}
        className="w-full flex items-center justify-between gap-3 px-4 py-3 text-white/80 hover:text-white bg-white/5 hover:bg-white/10 rounded-xl transition-all duration-300 border border-white/10 hover:border-white/20 hover:shadow-md transform hover:scale-[1.02] group"
        title="Switch sidebar theme"
      >
        <div className="flex items-center gap-3">
          <div 
            className="relative p-2 rounded-lg shadow-md transition-all duration-300 group-hover:shadow-lg"
            style={{ backgroundColor: currentTheme.accent }}
          >
            <Palette className="w-4 h-4 text-white" />
            <div className="absolute inset-0 bg-white/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
        </div>

        {/* Theme preview dots */}
        <div className="flex items-center gap-1">
          <div 
            className="w-3 h-3 rounded-full shadow-sm"
            style={{ backgroundColor: currentTheme.primary }}
          />
          <div 
            className="w-3 h-3 rounded-full shadow-sm"
            style={{ backgroundColor: currentTheme.secondary }}
          />
          <div 
            className="w-3 h-3 rounded-full shadow-sm"
            style={{ backgroundColor: currentTheme.accent }}
          />
        </div>
      </button>
    </div>
  );
}
'use client';

import React from 'react';
import { Calendar, Clock, X, MapPin } from 'lucide-react';

interface CourseCardProps {
  courseId: string;
  courseName: string;
  startDate: string;
  endDate: string;
  duration: number;
  position: number;
  onRemove: (courseId: string) => void;
  isFirst: boolean;
}

export default function CourseCard({
  courseId,
  courseName,
  startDate,
  endDate,
  duration,
  position,
  onRemove,
  isFirst
}: CourseCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getDateColor = (dateString: string, isStart: boolean) => {
    const date = new Date(dateString);
    const dayOfWeek = date.getDay();

    if (isStart && dayOfWeek === 1) { // Monday
      return 'text-green-600';
    } else if (!isStart && dayOfWeek === 0) { // Sunday
      return 'text-blue-600';
    }
    return 'text-gray-900';
  };

  return (
    <div className="bg-gray-50 rounded-lg p-6 border border-gray-200 relative">
      {/* Course Position Badge */}
      <div className="absolute -top-3 -left-3 bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold shadow-lg">
        {position}
      </div>

      {/* Remove Button (only show for non-first courses) */}
      {!isFirst && (
        <button
          onClick={() => onRemove(courseId)}
          className="absolute -top-2 -right-2 bg-red-600 hover:bg-red-700 text-white rounded-full w-6 h-6 flex items-center justify-center transition-colors"
          title="Remove this course"
        >
          <X className="w-4 h-4" />
        </button>
      )}

      {/* Course Header */}
      <div className="mb-4">
        <h4 className="text-lg font-semibold text-gray-900 mb-2 pr-8">
          {courseName}
        </h4>
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Clock className="w-4 h-4" />
          <span>{duration} weeks duration</span>
        </div>
      </div>

      {/* Course Dates */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Start Date */}
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center gap-2 mb-2">
            <Calendar className="w-4 h-4 text-green-600" />
            <span className="text-sm font-medium text-gray-700">Start Date</span>
          </div>
          <p className={`text-sm font-semibold ${getDateColor(startDate, true)}`}>
            {formatDate(startDate)}
          </p>
          {new Date(startDate).getDay() === 1 && (
            <p className="text-xs text-green-600 mt-1">✓ Adjusted to Monday</p>
          )}
        </div>

        {/* End Date */}
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center gap-2 mb-2">
            <Calendar className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-gray-700">End Date</span>
          </div>
          <p className={`text-sm font-semibold ${getDateColor(endDate, false)}`}>
            {formatDate(endDate)}
          </p>
          {new Date(endDate).getDay() === 0 && (
            <p className="text-xs text-blue-600 mt-1">✓ Adjusted to Sunday</p>
          )}
        </div>
      </div>

      {/* Course Flow Indicator */}
      {!isFirst && (
        <div className="absolute -left-8 top-1/2 transform -translate-y-1/2 hidden md:block">
          <div className="w-6 h-0.5 bg-blue-600"></div>
          <div className="w-0 h-0 border-l-4 border-l-blue-600 border-t-2 border-t-transparent border-b-2 border-b-transparent ml-6"></div>
        </div>
      )}
    </div>
  );
}

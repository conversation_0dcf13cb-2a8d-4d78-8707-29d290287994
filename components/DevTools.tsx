'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  RefreshCw,
  Trash2,
  Settings,
  X,
  Alert<PERSON><PERSON>gle,
  CheckCircle,
  Zap
} from 'lucide-react';

interface DevToolsProps {
  version?: string;
}

export default function DevTools({ version = '1.0.0' }: DevToolsProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [lastClearTime, setLastClearTime] = useState<string | null>(null);

  // Only show in development environment
  useEffect(() => {
    const isDev = process.env.NODE_ENV === 'development' ||
                  window.location.hostname === 'localhost' ||
                  window.location.hostname === '127.0.0.1';
    setIsVisible(isDev);

    // Check if we need to show update notification
    const storedVersion = localStorage.getItem('app-version');
    if (storedVersion && storedVersion !== version) {
      setIsOpen(true);
    }
    localStorage.setItem('app-version', version);

    // Get last clear time
    const lastClear = localStorage.getItem('last-cache-clear');
    setLastClearTime(lastClear);
  }, [version]);

  const handleHardRefresh = () => {
    // Force a hard refresh
    window.location.reload();
  };

  const handleClearLocalStorage = () => {
    try {
      localStorage.clear();
      setLastClearTime(new Date().toLocaleTimeString());
      localStorage.setItem('last-cache-clear', new Date().toLocaleTimeString());
      alert('✅ Local Storage cleared successfully!');
    } catch (error) {
      alert('❌ Failed to clear Local Storage');
    }
  };

  const handleClearSessionStorage = () => {
    try {
      sessionStorage.clear();
      alert('✅ Session Storage cleared successfully!');
    } catch (error) {
      alert('❌ Failed to clear Session Storage');
    }
  };

  const handleClearAllCache = async () => {
    try {
      // Clear localStorage and sessionStorage
      localStorage.clear();
      sessionStorage.clear();

      // Clear service worker cache if available
      if ('serviceWorker' in navigator && 'caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      }

      setLastClearTime(new Date().toLocaleTimeString());
      localStorage.setItem('last-cache-clear', new Date().toLocaleTimeString());

      alert('✅ All caches cleared! Page will refresh...');

      // Force hard refresh after clearing
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      alert('❌ Failed to clear all caches');
    }
  };

  const handleForceReload = () => {
    // Add timestamp to force cache bust
    const url = new URL(window.location.href);
    url.searchParams.set('_t', Date.now().toString());
    window.location.href = url.toString();
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Floating Dev Tools Button */}
      {!isOpen && (
        <div className="fixed bottom-4 right-4 z-50">
          <Button
            onClick={() => setIsOpen(true)}
            className="rounded-full w-12 h-12 bg-orange-500 hover:bg-orange-600 text-white shadow-lg"
            title="Open Dev Tools"
          >
            <Settings className="w-5 h-5" />
          </Button>
        </div>
      )}

      {/* Dev Tools Panel */}
      {isOpen && (
        <div className="fixed bottom-4 right-4 z-50 w-80">
          <Card className="shadow-2xl border-orange-200 bg-white">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Zap className="w-4 h-4 text-orange-500" />
                  Dev Tools
                  <span className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded">
                    v{version}
                  </span>
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                  className="h-6 w-6 p-0"
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              {/* Cache Status */}
              <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                <div className="flex items-center gap-1 mb-1">
                  <CheckCircle className="w-3 h-3 text-green-500" />
                  <span>Development Mode Active</span>
                </div>
                {lastClearTime && (
                  <div>Last cache clear: {lastClearTime}</div>
                )}
              </div>

              {/* Quick Actions */}
              <div className="space-y-2">
                <Button
                  onClick={handleHardRefresh}
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs"
                >
                  <RefreshCw className="w-3 h-3 mr-2" />
                  Hard Refresh (Ctrl+Shift+R)
                </Button>

                <Button
                  onClick={handleForceReload}
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs"
                >
                  <Zap className="w-3 h-3 mr-2" />
                  Force Reload (Cache Bust)
                </Button>

                <Button
                  onClick={handleClearLocalStorage}
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs"
                >
                  <Trash2 className="w-3 h-3 mr-2" />
                  Clear Local Storage
                </Button>

                <Button
                  onClick={handleClearSessionStorage}
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs"
                >
                  <Trash2 className="w-3 h-3 mr-2" />
                  Clear Session Storage
                </Button>

                <Button
                  onClick={handleClearAllCache}
                  variant="destructive"
                  size="sm"
                  className="w-full justify-start text-xs"
                >
                  <AlertTriangle className="w-3 h-3 mr-2" />
                  Clear All Cache & Reload
                </Button>
              </div>

              {/* Instructions */}
              <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded border border-blue-200">
                <div className="font-medium text-blue-700 mb-1">💡 Cache Issues?</div>
                <div>Try &ldquo;Force Reload&rdquo; first, then &ldquo;Clear All Cache&rdquo; if needed.</div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  );
}

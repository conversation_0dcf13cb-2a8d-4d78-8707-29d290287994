'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Calculator,
  Calendar,
  Menu,
  X,
  GraduationCap,
  ChevronRight,
  DollarSign,
  LogOut,
  User,
  Activity,
  Sparkles,
  Database,
  MessageSquare,
  ChevronLeft,
  Palette,
} from "lucide-react";
import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useSidebarTheme } from "@/contexts/SidebarThemeContext";

export default function Sidebar() {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const { isAuthenticated, user, logout } = useAuth();
  const { currentTheme, nextTheme, isCollapsed, toggleCollapsed } = useSidebarTheme();

  const toolCategories = [
    {
      name: "Academic Tools",
      tools: [
        {
          name: "Deferment Calculator",
          href: "/deferment-calculator",
          icon: Calculator,
          description: "Calculate course deferrals and adjustments",
          iconColor: "from-emerald-400 to-emerald-600",
          iconBg: "bg-emerald-500/20",
        },
        {
          name: "CT Calculator",
          href: "/ct-calculator",
          icon: MessageSquare,
          description: "Credit Transfer application assistant with AI guidance",
          iconColor: "from-purple-400 to-purple-600",
          iconBg: "bg-purple-500/20",
        },
        {
          name: "Fee Calculator",
          href: "/fee-calculator",
          icon: DollarSign,
          description: "Calculate payment coverage and allocations",
          iconColor: "from-yellow-400 to-yellow-600",
          iconBg: "bg-yellow-500/20",
        },
        {
          name: "Saved Calculations",
          href: "/admin/saved-calculations",
          icon: Database,
          description: "View and manage saved deferment calculations",
          iconColor: "from-cyan-400 to-cyan-600",
          iconBg: "bg-cyan-500/20",
        },
      ],
    },
    {
      name: "Course Information",
      tools: [
        {
          name: "Intake Dates",
          href: "/intake-dates",
          icon: Calendar,
          description: "View available course start dates",
          iconColor: "from-rose-400 to-rose-600",
          iconBg: "bg-rose-500/20",
        },
      ],
    },
    // Temporarily disabled - Data Management section
    // {
    //   name: "Data Management",
    //   tools: [
    //     {
    //       name: "Google Sheets Import",
    //       href: "/admin/import-sheets",
    //       icon: FileSpreadsheet,
    //       description: "Import clean course data from Google Sheets",
    //     },
    //     {
    //       name: "Database Sync",
    //       href: "/admin/sync",
    //       icon: Database,
    //       description: "Sync JSON data to database",
    //     },
    //   ],
    // },
  ];

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  const closeSidebar = () => {
    setIsOpen(false);
  };


  return (
    <>
      {/* Mobile menu button with modern styling */}
      <button
        onClick={toggleSidebar}
        className="xl:hidden fixed top-4 left-4 z-50 group"
      >
        <div className="p-2 sm:p-3 bg-white/90 backdrop-blur-md border border-gray-200/80 text-gray-700 rounded-xl hover:bg-white hover:border-blue-200 transition-all duration-300 shadow-lg hover:shadow-xl">
          {isOpen ? (
            <X
              size={18}
              className="sm:w-5 sm:h-5 transition-all duration-300 group-hover:rotate-90"
            />
          ) : (
            <Menu
              size={18}
              className="sm:w-5 sm:h-5 transition-all duration-300 group-hover:scale-110"
            />
          )}
        </div>
      </button>

      {/* Enhanced overlay for mobile */}
      {isOpen && (
        <div
          className="xl:hidden fixed inset-0 bg-black/40 backdrop-blur-sm z-40 transition-all duration-300"
          onClick={closeSidebar}
        />
      )}

      {/* Modern sidebar with dynamic theme background and improved responsive sizing */}
      <aside
        className={`
        fixed xl:static xl:translate-x-0 inset-y-0 left-0 z-50
        shadow-2xl flex flex-col
        transition-all duration-500 ease-out overflow-hidden
        ${isOpen ? "translate-x-0" : "-translate-x-full xl:translate-x-0"}
        ${isCollapsed
          ? "w-12 sm:w-14 md:w-16 xl:w-16 2xl:w-20"
          : "w-72 sm:w-80 md:w-80 xl:w-80 2xl:w-96"
        }
      `}
        style={{
          backgroundColor: currentTheme.primary
        }}
      >
        {/* Enhanced header with modern branding and collapse button */}
        <div className="p-4 border-b border-white/20">
          <div className="flex items-center gap-3">
            {/* Modern logo with dynamic theme gradient */}
            <div
              className="relative w-10 h-10 rounded-xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-all duration-300"
              style={{
                background: `linear-gradient(135deg, ${currentTheme.secondary}, ${currentTheme.accent})`
              }}
            >
              <div
                className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                style={{
                  background: `linear-gradient(135deg, ${currentTheme.accent}, ${currentTheme.secondary})`
                }}
              ></div>
              <GraduationCap className="w-6 h-6 text-white relative z-10" />
              <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>

            {/* Enhanced brand text */}
            {!isCollapsed && (
              <div className="flex-1">
                <h1 className="text-lg font-black text-white leading-tight tracking-tight">
                  EduPace
                </h1>
                <div className="flex items-center gap-2 mt-0.5">
                  <p className="text-sm text-white/80 font-bold">
                    Educational Platform
                  </p>
                  <div className="flex items-center gap-1">
                    <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-xs text-green-300 font-bold">
                      Live
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Desktop collapse toggle button with improved responsive sizing */}
            <button
              onClick={toggleCollapsed}
              className="hidden xl:flex w-7 h-7 md:w-8 md:h-8 2xl:w-9 2xl:h-9 items-center justify-center rounded-lg bg-white/10 hover:bg-white/20 transition-all duration-300 text-white/70 hover:text-white ml-auto"
              title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              <ChevronLeft
                className={`w-3.5 h-3.5 md:w-4 md:h-4 2xl:w-5 2xl:h-5 transition-transform duration-300 ${isCollapsed ? 'rotate-180' : ''}`}
              />
            </button>
          </div>
        </div>

        {/* Enhanced navigation with modern styling */}
        <nav className="flex-1 p-4 overflow-y-auto custom-scrollbar">
          {!isCollapsed && (
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-3">
                <Sparkles className="w-4 h-4 text-blue-300" />
                <h2 className="text-sm font-bold text-white tracking-wide"></h2>
              </div>
            </div>
          )}

          <div className={`${isCollapsed ? 'space-y-2' : 'space-y-6'}`}>
            {toolCategories.map((category) => (
              <div key={category.name} className="space-y-2">
                {!isCollapsed && (
                  <div className="flex items-center gap-2 px-1">
                    <div
                      className="w-1.5 h-1.5 rounded-full"
                      style={{
                        background: `linear-gradient(90deg, ${currentTheme.secondary}, ${currentTheme.accent})`
                      }}
                    ></div>
                    <h3 className="text-xs font-bold text-white/70 uppercase tracking-wider">
                      {category.name}
                    </h3>
                  </div>
                )}

                <ul className={`${isCollapsed ? 'space-y-2' : 'space-y-1'}`}>
                  {category.tools.map((tool) => {
                    const isActive = pathname === tool.href;
                    const Icon = tool.icon;

                    return (
                      <li key={tool.name}>
                        <Link
                          href={tool.href}
                          onClick={closeSidebar}
                          className={`
                            group relative block transition-all duration-300
                            ${isCollapsed
                              ? 'p-3 rounded-xl mx-auto w-12 h-12 flex items-center justify-center'
                              : 'px-4 py-4 rounded-2xl transform hover:scale-[1.02] hover:-translate-y-0.5'
                            }
                            ${
                              isActive
                                ? "bg-white/20 text-white shadow-lg border border-white/30"
                                : "text-white/80 hover:bg-white/10 hover:text-white hover:shadow-md"
                            }
                          `}
                          title={isCollapsed ? tool.name : undefined}
                        >
                          {isActive && !isCollapsed && (
                            <div
                              className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-8 rounded-r-full"
                              style={{
                                background: `linear-gradient(180deg, ${currentTheme.secondary}, ${currentTheme.accent})`
                              }}
                            ></div>
                          )}

                          {isCollapsed ? (
                            <div
                              className={`
                                relative p-1 rounded-lg transition-all duration-300
                                ${
                                  isActive
                                    ? `bg-gradient-to-r ${tool.iconColor} text-white shadow-lg`
                                    : `${tool.iconBg} text-white group-hover:bg-gradient-to-r group-hover:${tool.iconColor} group-hover:text-white group-hover:shadow-lg`
                                }
                              `}
                            >
                              <Icon size={16} />
                              {isActive && (
                                <div className="absolute inset-0 bg-white/20 rounded-lg"></div>
                              )}
                            </div>
                          ) : (
                            <div className="flex items-center space-x-4">
                              <div
                                className={`
                                  relative p-2.5 rounded-xl transition-all duration-300
                                  ${
                                    isActive
                                      ? `bg-gradient-to-r ${tool.iconColor} text-white shadow-lg`
                                      : `${tool.iconBg} text-white group-hover:bg-gradient-to-r group-hover:${tool.iconColor} group-hover:text-white group-hover:shadow-lg`
                                  }
                                `}
                              >
                                <Icon size={18} />
                                {isActive && (
                                  <div className="absolute inset-0 bg-white/20 rounded-xl"></div>
                                )}
                              </div>

                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between">
                                  <span
                                    className={`
                                      text-sm font-bold transition-colors duration-300 truncate
                                      ${
                                        isActive
                                          ? "text-white"
                                          : "text-white/90 group-hover:text-white"
                                      }
                                    `}
                                  >
                                    {tool.name}
                                  </span>
                                  <ChevronRight
                                    size={16}
                                    className={`
                                      flex-shrink-0 ml-2 transition-all duration-300
                                      ${
                                        isActive
                                          ? "text-white rotate-90 scale-110"
                                          : "text-white/60 group-hover:text-white group-hover:translate-x-1"
                                      }
                                    `}
                                  />
                                </div>
                                <p
                                  className={`
                                    text-xs font-medium transition-colors duration-300 mt-1 leading-relaxed
                                    ${
                                      isActive
                                        ? "text-white/90"
                                        : "text-white/70 group-hover:text-white/90"
                                    }
                                  `}
                                >
                                  {tool.description}
                                </p>
                              </div>
                            </div>
                          )}
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              </div>
            ))}
          </div>
        </nav>

        {/* Enhanced footer with modern styling */}
        <div className="p-3 border-t border-white/20">
          {isAuthenticated && user && !isCollapsed ? (
            <>
              {/* Compact User Information */}
              <div className="flex items-center gap-2 mb-2 p-2 bg-white/5 rounded-lg">
                <div className="relative w-6 h-6 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-md flex items-center justify-center">
                  <User className="w-3 h-3 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-xs font-bold text-white truncate">
                    {user.email}
                  </p>
                  <div className="flex items-center gap-1">
                    <div className="w-1 h-1 bg-green-400 rounded-full animate-pulse"></div>
                    <p className="text-xs text-green-300 font-medium">
                      Authenticated
                    </p>
                  </div>
                </div>
              </div>

              {/* Compact Logout Button */}
              <button
                onClick={logout}
                className="w-full flex items-center justify-center gap-1 px-2 py-1.5 text-xs font-bold text-red-300 bg-red-900/20 hover:bg-red-900/40 rounded-lg transition-all duration-300 mb-2"
              >
                <LogOut className="w-3 h-3" />
                Sign Out
              </button>
            </>
          ) : isAuthenticated && user && isCollapsed ? (
            <>
              {/* Collapsed User Icon */}
              <div className="flex justify-center mb-2">
                <div className="relative w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
              </div>
              {/* Collapsed Logout Button */}
              <button
                onClick={logout}
                className="w-full flex items-center justify-center p-2 text-red-300 bg-red-900/20 hover:bg-red-900/40 rounded-lg transition-all duration-300 mb-2"
                title="Sign Out"
              >
                <LogOut className="w-4 h-4" />
              </button>
            </>
          ) : null}

          {/* Compact System Status and Theme */}
          <div className="space-y-1">
            {!isCollapsed ? (
              <>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-xs font-bold text-white">System Online</span>
                  </div>
                  <span className="text-xs text-white/70 font-bold">v2.0</span>
                </div>

                <div className="flex items-center justify-between pt-1 border-t border-white/10">
                  <span className="text-xs font-bold text-white/80">EduPace</span>
                  <span className="text-xs text-white/50">© 2025</span>
                </div>

                {/* Compact Theme Toggle */}
                <button
                  onClick={nextTheme}
                  className="w-full flex items-center justify-center gap-2 p-2 text-white/70 hover:text-white bg-white/5 hover:bg-white/10 rounded-lg transition-all duration-300"
                  title="Switch theme"
                >
                  <Palette className="w-4 h-4" />
                  <div className="flex gap-1">
                    <div
                      className="w-2 h-2 rounded-full"
                      style={{ backgroundColor: currentTheme.primary }}
                    />
                    <div
                      className="w-2 h-2 rounded-full"
                      style={{ backgroundColor: currentTheme.secondary }}
                    />
                    <div
                      className="w-2 h-2 rounded-full"
                      style={{ backgroundColor: currentTheme.accent }}
                    />
                  </div>
                </button>
              </>
            ) : (
              <>
                {/* Collapsed status indicator */}
                <div className="flex justify-center mb-1">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                </div>

                {/* Collapsed Theme Toggle */}
                <button
                  onClick={nextTheme}
                  className="w-full flex items-center justify-center p-2 text-white/70 hover:text-white bg-white/5 hover:bg-white/10 rounded-lg transition-all duration-300"
                  title="Switch theme"
                >
                  <Palette className="w-4 h-4" />
                </button>
              </>
            )}
          </div>
        </div>
      </aside>
    </>
  );
}

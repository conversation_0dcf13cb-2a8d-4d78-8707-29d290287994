// Google Sheets to SQL Generator
// This script reads your Google Sheets data and generates SQL statements
// Run this in Node.js to generate the SQL statements you need

const fs = require('fs');

// Based on your Google Sheets structure:
// Column A: Faculty
// Column B: Course Name  
// Column C: VET Code
// Column D: CRICOS Code

// Sample data from your AIBT sheet (first few rows)
const sampleData = [
  ['Faculty', 'Course Name', 'VET Code', 'CRICOS Code'], // Header row
  ['ACE AVIATION AEROSPACE ACADEMY', 'Diploma of Aviation (Aviation Management)', 'AVI50119', '101215'],
  ['BESPOKE GRAMMAR SCHOOL OF ENGLISH', 'General English (Beginner to Upper Intermediate)', 'N/A', '089486D'],
  ['BRANSON SCHOOL OF BUSINESS', 'Certificate IV in Accounting and Bookkeeping', 'FNS40222', '111047F'],
  ['BRANSON SCHOOL OF BUSINESS', 'Diploma of Accounting', 'FNS50222', '113810D']
];

function isValidCRICOSCode(code) {
  // Skip invalid codes
  if (!code || code === 'N/A' || code.startsWith('CR')) {
    return false;
  }
  
  // CRICOS codes can be 6 digits, or 6 characters with letter at the end
  // Examples: 101215, 089486D, 111047F
  const cricosPattern = /^(\d{6}|\d{5}[A-Z])$/;
  return cricosPattern.test(code);
}

function generateSQL(collegeName, sheetData) {
  let sql = `-- SQL for ${collegeName} college\n`;
  sql += `-- Clear existing data for ${collegeName} (optional)\n`;
  sql += `DELETE FROM courses WHERE college_name = '${collegeName}';\n\n`;
  
  // Skip header row (index 0)
  for (let i = 1; i < sheetData.length; i++) {
    const row = sheetData[i];
    const faculty = row[0];
    const courseName = row[1];
    const vetCode = row[2] === 'N/A' ? null : row[2];
    const cricosCode = row[3];
    
    // Skip invalid CRICOS codes
    if (!isValidCRICOSCode(cricosCode)) {
      console.log(`Skipping ${courseName} - Invalid CRICOS: ${cricosCode}`);
      continue;
    }
    
    // Generate INSERT statement
    sql += `INSERT INTO courses (college_name, faculty, course_name, vet_code, cricos_code, created_at, updated_at) VALUES (\n`;
    sql += `  '${collegeName}',\n`;
    sql += `  '${faculty.replace(/'/g, "''")}',\n`;
    sql += `  '${courseName.replace(/'/g, "''")}',\n`;
    sql += `  ${vetCode ? `'${vetCode}'` : 'NULL'},\n`;
    sql += `  '${cricosCode}',\n`;
    sql += `  NOW(),\n`;
    sql += `  NOW()\n`;
    sql += `);\n\n`;
  }
  
  return sql;
}

// Generate SQL for sample data
const sql = generateSQL('AIBT', sampleData);

console.log('Generated SQL:');
console.log(sql);

// Save to file
fs.writeFileSync('generated-sql.sql', sql);
console.log('SQL saved to generated-sql.sql');

// Instructions for user
console.log('\n--- INSTRUCTIONS ---');
console.log('1. I (Claude) will read your Google Sheets data');
console.log('2. I will generate SQL statements like the ones above');
console.log('3. You copy and paste the SQL into your database');
console.log('4. Your database will be updated with clean data');
console.log('5. Then you can use your existing /admin/sync for intake dates');
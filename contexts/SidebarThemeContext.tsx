'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

export interface SidebarTheme {
  id: string;
  name: string;
  primary: string;
  secondary: string;
  accent: string;
  border: string;
}

export const sidebarThemes: SidebarTheme[] = [
  {
    id: 'ocean-blue',
    name: 'Ocean Blue',
    primary: '#125c87',
    secondary: '#1e73a0',
    accent: '#2563eb',
    border: 'rgba(59, 130, 246, 0.3)',
  },
  {
    id: 'forest-green',
    name: 'Forest Green',
    primary: '#134e4a',
    secondary: '#0f766e',
    accent: '#059669',
    border: 'rgba(16, 185, 129, 0.3)',
  },
  {
    id: 'royal-purple',
    name: '<PERSON> Purple',
    primary: '#581c87',
    secondary: '#7c3aed',
    accent: '#8b5cf6',
    border: 'rgba(139, 92, 246, 0.3)',
  },
  {
    id: 'sunset-orange',
    name: 'Sunset Orange',
    primary: '#9a3412',
    secondary: '#ea580c',
    accent: '#f97316',
    border: 'rgba(249, 115, 22, 0.3)',
  },
  {
    id: 'midnight-black',
    name: '<PERSON> Black',
    primary: '#0f172a',
    secondary: '#1e293b',
    accent: '#334155',
    border: 'rgba(148, 163, 184, 0.3)',
  },
  {
    id: 'rose-pink',
    name: 'Rose Pink',
    primary: '#881337',
    secondary: '#be185d',
    accent: '#ec4899',
    border: 'rgba(236, 72, 153, 0.3)',
  },
  {
    id: 'crimson-red',
    name: 'Crimson Red',
    primary: '#7f1d1d',
    secondary: '#dc2626',
    accent: '#ef4444',
    border: 'rgba(239, 68, 68, 0.3)',
  },
  {
    id: 'golden-yellow',
    name: 'Golden Yellow',
    primary: '#78350f',
    secondary: '#d97706',
    accent: '#f59e0b',
    border: 'rgba(245, 158, 11, 0.3)',
  },
  {
    id: 'teal-cyan',
    name: 'Teal Cyan',
    primary: '#0f4c75',
    secondary: '#0891b2',
    accent: '#06b6d4',
    border: 'rgba(6, 182, 212, 0.3)',
  },
  {
    id: 'violet-indigo',
    name: 'Violet Indigo',
    primary: '#3730a3',
    secondary: '#4f46e5',
    accent: '#6366f1',
    border: 'rgba(99, 102, 241, 0.3)',
  },
  {
    id: 'emerald-jade',
    name: 'Emerald Jade',
    primary: '#064e3b',
    secondary: '#047857',
    accent: '#10b981',
    border: 'rgba(16, 185, 129, 0.3)',
  },
  {
    id: 'amber-gold',
    name: 'Amber Gold',
    primary: '#92400e',
    secondary: '#d97706',
    accent: '#f59e0b',
    border: 'rgba(245, 158, 11, 0.3)',
  },
  {
    id: 'slate-storm',
    name: 'Slate Storm',
    primary: '#0f172a',
    secondary: '#334155',
    accent: '#64748b',
    border: 'rgba(100, 116, 139, 0.3)',
  },
  {
    id: 'cherry-blossom',
    name: 'Cherry Blossom',
    primary: '#9d174d',
    secondary: '#db2777',
    accent: '#f472b6',
    border: 'rgba(244, 114, 182, 0.3)',
  },
  {
    id: 'deep-sea',
    name: 'Deep Sea',
    primary: '#1e3a8a',
    secondary: '#2563eb',
    accent: '#3b82f6',
    border: 'rgba(59, 130, 246, 0.3)',
  },
  {
    id: 'lime-green',
    name: 'Lime Green',
    primary: '#365314',
    secondary: '#65a30d',
    accent: '#84cc16',
    border: 'rgba(132, 204, 22, 0.3)',
  },
  {
    id: 'magenta-fuchsia',
    name: 'Magenta Fuchsia',
    primary: '#86198f',
    secondary: '#c026d3',
    accent: '#e879f9',
    border: 'rgba(232, 121, 249, 0.3)',
  },
  {
    id: 'bronze-copper',
    name: 'Bronze Copper',
    primary: '#7c2d12',
    secondary: '#ea580c',
    accent: '#fb923c',
    border: 'rgba(251, 146, 60, 0.3)',
  },
  {
    id: 'navy-steel',
    name: 'Navy Steel',
    primary: '#1e293b',
    secondary: '#475569',
    accent: '#64748b',
    border: 'rgba(100, 116, 139, 0.3)',
  },
  {
    id: 'coral-peach',
    name: 'Coral Peach',
    primary: '#9f1239',
    secondary: '#e11d48',
    accent: '#f43f5e',
    border: 'rgba(244, 63, 94, 0.3)',
  },
];

interface SidebarThemeContextType {
  currentTheme: SidebarTheme;
  setTheme: (themeId: string) => void;
  themes: SidebarTheme[];
  nextTheme: () => void;
  isCollapsed: boolean;
  setIsCollapsed: (collapsed: boolean) => void;
  toggleCollapsed: () => void;
}

const SidebarThemeContext = createContext<SidebarThemeContextType | undefined>(undefined);

export function SidebarThemeProvider({ children }: { children: React.ReactNode }) {
  const [currentTheme, setCurrentTheme] = useState<SidebarTheme>(sidebarThemes[0]);
  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);

  // Load theme and collapsed state from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('sidebar-theme');
    if (savedTheme) {
      const theme = sidebarThemes.find(t => t.id === savedTheme);
      if (theme) {
        setCurrentTheme(theme);
      }
    }

    const savedCollapsed = localStorage.getItem('sidebar-collapsed');
    if (savedCollapsed !== null) {
      setIsCollapsed(JSON.parse(savedCollapsed));
    }
  }, []);

  const setTheme = (themeId: string) => {
    const theme = sidebarThemes.find(t => t.id === themeId);
    if (theme) {
      setCurrentTheme(theme);
      localStorage.setItem('sidebar-theme', themeId);
    }
  };

  const nextTheme = () => {
    const currentIndex = sidebarThemes.findIndex(t => t.id === currentTheme.id);
    const nextIndex = (currentIndex + 1) % sidebarThemes.length;
    setTheme(sidebarThemes[nextIndex].id);
  };

  const toggleCollapsed = () => {
    const newCollapsed = !isCollapsed;
    setIsCollapsed(newCollapsed);
    localStorage.setItem('sidebar-collapsed', JSON.stringify(newCollapsed));
  };

  return (
    <SidebarThemeContext.Provider value={{
      currentTheme,
      setTheme,
      themes: sidebarThemes,
      nextTheme,
      isCollapsed,
      setIsCollapsed: (collapsed: boolean) => {
        setIsCollapsed(collapsed);
        localStorage.setItem('sidebar-collapsed', JSON.stringify(collapsed));
      },
      toggleCollapsed,
    }}>
      {children}
    </SidebarThemeContext.Provider>
  );
}

export function useSidebarTheme() {
  const context = useContext(SidebarThemeContext);
  if (context === undefined) {
    throw new Error('useSidebarTheme must be used within a SidebarThemeProvider');
  }
  return context;
}
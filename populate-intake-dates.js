// Script to populate intake dates into the Google Sheet
const fs = require('fs');

console.log('📖 Reading intake dates CSV...');

// Read the CSV file
const csvData = fs.readFileSync('intake-dates-from-json.csv', 'utf8');
const lines = csvData.split('\n').filter(line => line.trim());

// Skip header line
const dataLines = lines.slice(1);

console.log(`📊 Processing ${dataLines.length} courses...`);

// Process each line and create rows for Google Sheets
const intakeRows = [];
let coursesProcessed = 0;

dataLines.forEach((line, index) => {
  const columns = line.split(',');
  
  if (columns.length >= 2) {
    const collegeName = columns[0];
    const courseName = columns[1];
    
    // Get the intake dates (limiting to first 20 dates)
    const intakeDates = columns.slice(2, 22).map(date => date.trim() || '');
    
    // Create row for Google Sheets
    const row = [collegeName, courseName, ...intakeDates];
    intakeRows.push(row);
    coursesProcessed++;
    
    // Log progress
    if (coursesProcessed % 10 === 0) {
      console.log(`✓ Processed ${coursesProcessed} courses...`);
    }
  }
});

console.log(`\n✅ Total courses processed: ${coursesProcessed}`);
console.log(`📄 Rows ready for Google Sheets: ${intakeRows.length}`);

// Show first few rows as sample
console.log('\n🔍 Sample data (first 3 rows):');
intakeRows.slice(0, 3).forEach((row, index) => {
  console.log(`${index + 1}. ${row[0]} - ${row[1]}`);
  console.log(`   Dates: ${row.slice(2, 7).join(', ')}...`);
});

// Save processed data as JSON for easier handling
const processedData = {
  headers: ['college_name', 'course_name', 'intake_date_1', 'intake_date_2', 'intake_date_3', 'intake_date_4', 'intake_date_5', 'intake_date_6', 'intake_date_7', 'intake_date_8', 'intake_date_9', 'intake_date_10', 'intake_date_11', 'intake_date_12', 'intake_date_13', 'intake_date_14', 'intake_date_15', 'intake_date_16', 'intake_date_17', 'intake_date_18', 'intake_date_19', 'intake_date_20'],
  rows: intakeRows
};

fs.writeFileSync('intake-data-for-sheets.json', JSON.stringify(processedData, null, 2));
console.log('\n💾 Data saved to intake-data-for-sheets.json');

// College mapping analysis
console.log('\n🏫 College Analysis:');
const collegeStats = {};
intakeRows.forEach(row => {
  const college = row[0];
  if (!collegeStats[college]) {
    collegeStats[college] = 0;
  }
  collegeStats[college]++;
});

Object.keys(collegeStats).forEach(college => {
  console.log(`   ${college}: ${collegeStats[college]} courses`);
});

console.log('\n🚨 College Name Mapping Needed:');
console.log('   JSON names → Google Sheets names');
console.log('   AIBT → AIBT (✓ matches)');
console.log('   AVTA → AVT (needs mapping)');
console.log('   AIBT-I → AAIBT (needs mapping)');
console.log('   Reach College → REACH (needs mapping)');
console.log('   Brooklyn → ??? (needs mapping)');
console.log('   NPA → NPA (✓ matches)');

console.log('\n🔄 Ready to upload to Google Sheets!');
console.log('   Spreadsheet ID: 1BMccVuoTOdSVtDdFybCYT_36n3p6natDUnWGRjuN1Qo');
console.log('   Sheet: Intake Dates');
console.log('   Range: A2:V' + (intakeRows.length + 1));
# Student Support Tools Platform

A comprehensive Next.js web application that provides multiple tools to support students and educational administrators in managing various aspects of academic life.

## Core Features

- **Deferment Calculator**: Calculate deferment periods and adjusted resumption dates
- **Course Information**: Manage course sequences and intake dates
- **Data Management**: Import/export and synchronization tools
- **Multi-tool Platform**: Extensible architecture for adding new student support tools

## Key Use Cases

- Students accessing multiple support tools from one platform
- Educational administrators managing course schedules and data
- Calculating course deferrals and finding next available intake dates
- Importing and synchronizing course data from various sources
- Providing a unified experience for student services

## Platform Architecture

- Modular tool-based system for easy extensibility
- Categorized navigation (Academic Tools, Course Information, Data Management)
- Consistent design system across all tools
- Responsive design for desktop and mobile use

## Data Sources

- Course and intake data stored in JSON files
- Student records in SQLite database
- Supabase integration for enhanced data management
- Google Sheets integration for data import

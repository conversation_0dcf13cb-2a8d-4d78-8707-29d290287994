# Technology Stack

## Framework & Runtime

- **Next.js 14.2.19** - React framework with App Router
- **React 18** - UI library
- **TypeScript 5** - Type safety and development experience
- **Node.js** - Runtime environment

## Styling & UI

- **Tailwind CSS 3.4.1** - Utility-first CSS framework
- **@tailwindcss/forms** - Form styling plugin
- **Lucide React** - Icon library
- Custom color palette with primary, accent, success, warning, error themes
- Glass-morphism and modern design system

## Database & Data

- **Supabase** - Primary database and backend services
- **SQLite** - Local database for student records (`data/student-deferment.db`)
- **JSON files** - Course and intake data storage
- **date-fns** - Date manipulation and formatting

## Testing

- **Jest 30** - Testing framework
- **@testing-library/react** - React component testing
- **@testing-library/jest-dom** - DOM testing utilities
- **jsdom** - DOM environment for tests

## Development Tools

- **ESLint** - Code linting with Next.js config
- **PostCSS** - CSS processing
- **SWC** - Fast TypeScript/JavaScript compiler

## Common Commands

### Development

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Testing

```bash
npm test             # Run tests once
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage report
```

### Data Management

```bash
node scripts/csvToJson.js      # Convert CSV data to JSON
node scripts/fixIntakeData.js  # Fix intake data formatting
python scripts/parseIntakeData.py # Parse intake data
```

## Path Aliases

- `@/*` maps to project root for clean imports

# Project Structure

## Root Level

- **Configuration files** - Next.js, TypeScript, Tai<PERSON><PERSON>, Jest configs
- **Data files** - `intake-dates.json`, `intake-dates-backup.json`
- **Package management** - `package.json`, `package-lock.json`

## Core Directories

### `/app` - Next.js App Router

- **Route-based structure** using Next.js 13+ App Router
- `page.tsx` - Route components
- `layout.tsx` - Layout components
- `loading.tsx` - Loading UI components
- `error.tsx` - Error boundary components

**Key Routes:**

- `/` - Redirects to deferment calculator
- `/deferment-calculator` - Main calculator interface
- `/intake-dates` - Intake dates viewer
- `/api/colleges` - API endpoints

### `/components` - Reusable React Components

- `DefermentCalculatorEnhanced.tsx` - Main calculator component
- `CourseCard.tsx` - Course display component
- `IntakeDatesViewer.tsx` - Intake dates display
- `Sidebar.tsx` - Navigation sidebar

### `/lib` - Shared Libraries and Utilities

- `/data` - Data management and course data handling
- `/supabase` - Supabase client and service layer
- `/utils` - Utility functions (date calculations, etc.)
- `/database` - Database-related code
- `types.ts` - TypeScript type definitions

### `/data` - Local Data Storage

- `student-deferment.db` - SQLite database for student records

### `/scripts` - Data Processing Scripts

- `csvToJson.js` - CSV to JSON conversion
- `fixIntakeData.js` - Data cleanup and formatting
- `parseIntakeData.py` - Python data parsing script

### `/__tests__` - Test Files

- **Mirror structure** of source code
- `/components` - Component tests
- `/lib` - Library and utility tests
- Follows Jest naming conventions

### `/.kiro` - Kiro IDE Configuration

- `/specs` - Feature specifications and requirements
- `/steering` - AI assistant guidance rules

## File Naming Conventions

- **React components** - PascalCase (e.g., `DefermentCalculator.tsx`)
- **Utilities and services** - camelCase (e.g., `dateUtils.ts`)
- **API routes** - lowercase with hyphens
- **Test files** - Match source file name with `.test.ts` suffix

## Import Patterns

- Use `@/` alias for clean imports from project root
- Relative imports for same-directory files
- Group imports: external libraries, internal modules, relative imports

## Data Flow

1. **JSON files** → Course and intake data
2. **SQLite database** → Student records
3. **Supabase** → Enhanced data management and sync
4. **Components** → UI state and user interactions

# Design Document

## Overview

This design transforms the Student Deferment Calculator into a modern, professional tool that leverages the existing design system while addressing UI/UX issues and simplifying the date selection logic. The enhanced calculator will provide users with full control over their course planning while maintaining a clean, intuitive interface.

## Architecture

### Component Structure

The enhanced calculator will maintain the existing React component structure but with significant improvements:

```
DefermentCalculatorEnhanced
├── Header Section (Hero with branding)
├── Form Container (Card-based layout)
│   ├── Student Information (Optional)
│   ├── Course Selection (Optional)
│   └── Date Calculation (Required)
├── Results Display (Professional cards)
└── Course Sequence Management (Enhanced UX)
```

### Design System Integration

The calculator will fully utilize the existing UI component library:
- `Card` components with `variant="elevated"` for main sections
- `Button` components with appropriate variants for different actions
- `Input` and `Select` components with consistent styling
- Proper color palette usage from the Tailwind configuration

## Components and Interfaces

### Enhanced Form Layout

**Professional Header**
- Clean hero section with calculator icon and title
- Subtle gradient background using existing design tokens
- Clear description of calculator functionality
- Consistent with other platform tools

**Card-Based Form Sections**
- Each logical group in separate elevated cards
- Proper spacing and visual hierarchy
- Optional field indicators clearly marked
- Progressive disclosure for advanced options

**Field Components**
```typescript
interface FormFieldProps {
  name: string;
  label: string;
  type?: 'text' | 'date' | 'select';
  required?: boolean;
  optional?: boolean;
  tooltip?: string;
  icon?: React.ComponentType;
  placeholder?: string;
}
```

### Simplified Date Logic

**User-Controlled Date Selection**
- Remove automatic date population from database
- Provide date picker for resumption date selection
- Maintain Monday/Sunday adjustment logic
- Clear indication of date adjustments

**Calculation Flow**
```typescript
interface CalculationInput {
  defermentStartDate: string;
  actualEndDate: string;
  resumptionDate: string; // User-selected, not auto-populated
  studentNumber?: string; // Optional
  selectedProvider?: string; // Optional
  selectedCourse?: string; // Optional
}

interface CalculationResult {
  adjustedStartDate: string; // Monday adjustment
  adjustedEndDate: string; // Sunday adjustment
  totalWeeks: number;
  courseDuration?: number; // If course selected
}
```

### Professional Results Display

**Results Card Layout**
- Clean, card-based results presentation
- Key metrics prominently displayed
- Visual timeline for course sequence
- Export and sharing options

**Course Sequence Visualization**
- Timeline-style layout for multiple courses
- Clear start/end date indicators
- Duration and gap calculations
- Easy course management (add/remove)

## Data Models

### Enhanced Form State
```typescript
interface FormData {
  // Required fields
  defermentStartDate: string;
  actualEndDate: string;
  resumptionDate: string;

  // Optional fields (no validation required)
  studentNumber?: string;
  selectedProvider?: string;
  selectedCourse?: string;
}

interface FormValidation {
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isValid: boolean;
}
```

### Course Sequence Management
```typescript
interface CourseSequence {
  id: string;
  position: number;
  courseName?: string; // Optional if course not selected
  startDate: string; // User-controlled
  endDate: string; // Calculated
  duration: number; // Calculated or estimated
  isUserDefined: boolean; // Track if dates are user-defined
}
```

## Error Handling

### Validation Strategy

**Required Field Validation**
- Only validate truly required fields (dates)
- Clear, specific error messages
- Real-time validation feedback
- Prevent form submission with errors

**Optional Field Handling**
- No validation errors for empty optional fields
- Enhanced functionality when optional data provided
- Graceful degradation when optional data missing

**Date Validation**
```typescript
const validateDates = (formData: FormData): ValidationErrors => {
  const errors: ValidationErrors = {};

  if (!formData.defermentStartDate) {
    errors.defermentStartDate = 'Deferment start date is required';
  }

  if (!formData.actualEndDate) {
    errors.actualEndDate = 'Actual end date is required';
  }

  if (!formData.resumptionDate) {
    errors.resumptionDate = 'Resumption date is required';
  }

  // Date logic validation
  if (formData.defermentStartDate && formData.actualEndDate) {
    if (new Date(formData.defermentStartDate) >= new Date(formData.actualEndDate)) {
      errors.actualEndDate = 'End date must be after start date';
    }
  }

  return errors;
};
```

### Error Display
- Inline field errors with clear messaging
- Summary error display for form-level issues
- Recovery suggestions for common errors
- Consistent error styling using design system

## Testing Strategy

### Component Testing
- Unit tests for form validation logic
- Component rendering tests with different states
- User interaction testing (form submission, field changes)
- Accessibility testing for form elements

### Integration Testing
- Date calculation accuracy testing
- Optional field behavior verification
- Course sequence management testing
- Results display formatting tests

### Visual Testing
- Responsive design verification
- Design system consistency checks
- Loading state appearance
- Error state presentation

### User Experience Testing
- Form completion flow testing
- Optional vs required field behavior
- Calculation result accuracy
- Export functionality verification

## Implementation Approach

### Phase 1: Core UI Enhancement
1. Implement new card-based layout
2. Integrate design system components
3. Add professional styling and animations
4. Improve responsive design

### Phase 2: Logic Simplification
1. Remove automatic date population
2. Make fields optional as specified
3. Simplify validation logic
4. Enhance user control over dates

### Phase 3: Results Enhancement
1. Redesign results display
2. Improve course sequence visualization
3. Add export capabilities
4. Enhance summary information

### Phase 4: Polish and Testing
1. Add loading states and transitions
2. Implement comprehensive error handling
3. Add accessibility improvements
4. Performance optimization

## Visual Design Specifications

### Color Scheme
- Primary: Use existing `primary-600` for main actions
- Success: `success-500` for positive feedback
- Error: `error-500` for validation errors
- Neutral: `gray-50` to `gray-900` for text and backgrounds

### Typography
- Headers: `text-2xl` to `text-3xl` with `font-bold`
- Body text: `text-base` with `font-medium`
- Labels: `text-sm` with `font-medium`
- Helper text: `text-sm` with `text-gray-600`

### Spacing and Layout
- Card padding: `p-8` for main content areas
- Section spacing: `space-y-8` between major sections
- Field spacing: `space-y-6` between form fields
- Grid layouts: `grid-cols-1 md:grid-cols-2` for responsive design

### Interactive Elements
- Hover states: Subtle scale and shadow changes
- Focus states: Ring-based focus indicators
- Loading states: Spinner animations with opacity changes
- Transitions: `transition-all duration-300` for smooth interactions

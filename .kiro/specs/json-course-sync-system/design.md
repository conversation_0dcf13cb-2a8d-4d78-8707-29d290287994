# Design Document

## Overview

The JSON Course Sync System is a robust, maintainable utility that intelligently imports intake date information from JSON files into an existing Supabase database structure. The system preserves all existing course metadata while updating only intake dates, using sophisticated pattern matching to map JSON course names to database records. Built with transaction safety, comprehensive logging, and configuration management for long-term reliability.

## Architecture

### Technology Stack

- **Runtime**: Node.js with TypeScript for type safety and maintainability
- **Database**: Supabase PostgreSQL with existing schema (providers → faculties → courses → locations → intakes)
- **Configuration**: JSON-based configuration files for mappings and patterns
- **Logging**: Structured logging with multiple output formats (console, file, JSON)
- **CLI Interface**: Commander.js for user-friendly command-line operations
- **Testing**: Jest for comprehensive unit and integration testing

### System Components

```
json-course-sync-system/
├── src/
│   ├── core/
│   │   ├── parser.ts           # JSON parsing and validation
│   │   ├── matcher.ts          # Course and location matching logic
│   │   ├── importer.ts         # Database import operations
│   │   └── validator.ts        # Data validation and quality checks
│   ├── config/
│   │   ├── mappings.ts         # Configuration management
│   │   ├── patterns.ts         # Regex patterns for extraction
│   │   └── database.ts         # Database connection config
│   ├── services/
│   │   ├── backup.service.ts   # Backup and rollback operations
│   │   ├── logger.service.ts   # Structured logging service
│   │   └── report.service.ts   # Import reporting and analytics
│   ├── utils/
│   │   ├── date.utils.ts       # Date parsing and validation
│   │   ├── string.utils.ts     # Text processing utilities
│   │   └── transaction.utils.ts # Database transaction helpers
│   └── cli/
│       ├── commands/           # CLI command implementations
│       ├── index.ts CLI entry point
│       └── interactive.ts     # Interactive mode for complex operations
├── config/
│   ├── location-mappings.json  # Location code to database mappings
│   ├── provider-mappings.json  # Provider name variations
│   ├── course-patterns.json    # VET code extraction patterns
│   └── import-settings.json    # General import configuration
├── tests/
│   ├── unit/                  # Unit tests for individual components
│   ├── integration/           # Integration tests with database
│   └── fixtures/              # Test data and mock JSON files
└── scripts/
    ├── setup.sh              # Initial setup and configuration
    ├── health-check.sh       # System health verification
    └── backup-cleanup.sh     # Automated backup maintenance
```

## Core Components Design

### 1. JSON Parser and Validator

```typescript
interface ParsedCourseData {
  provider: string;
  courseName: string;
  extractedVetCode: string | null;
  extractedLocations: string[];
  intakeDates: Date[];
  rawCourseName: string;
  confidence: number; // Matching confidence score
}

class JSONParser {
  async parseIntakeFile(filePath: string): Promise<ParsedCourseData[]> {
    // Parse JSON with comprehensive error handling
    // Extract VET codes using configurable patterns
    // Parse location codes from course names
    // Validate date formats and reject invalid dates
    // Return structured data with confidence scores
  }

  private extractVetCode(courseName: string): string | null {
    // Use configurable regex patterns to extract codes like:
    // BSB80120, CHC30121, FNS40222, etc.
  }

  private extractLocations(courseName: string): string[] {
    // Extract location codes from patterns like:
    // "(SYD, BSB & HOB)", "(Sydney)", "(Melbourne)"
  }
}
```

### 2. Intelligent Course Matcher

```typescript
interface MatchResult {
  courseId: string | null;
  locationIds: string[];
  confidence: number;
  matchType: "exact" | "fuzzy" | "manual";
  issues: string[];
}

class CourseMatcher {
  constructor(
    private supabaseService: SupabaseService,
    private config: MappingConfig
  ) {}

  async matchCourse(parsedData: ParsedCourseData): Promise<MatchResult> {
    // 1. Try exact VET code + provider match
    // 2. Try fuzzy course name matching
    // 3. Handle multiple matches with disambiguation
    // 4. Map location codes to database location IDs
    // 5. Return match result with confidence score
  }

  private async findCourseByVetCode(
    vetCode: string,
    providerName: string
  ): Promise<Course[]> {
    // Query database for courses matching VET code and provider
  }

  private mapLocationCodes(locationCodes: string[]): string[] {
    // Use configuration to map codes like:
    // "SYD" → "Sydney", "BSB" → "Brisbane", "HOB" → "Hobart"
  }
}
```

### 3. Transaction-Safe Importer

```typescript
class SafeImporter {
  constructor(
    private supabaseService: SupabaseService,
    private backupService: BackupService,
    private logger: LoggerService
  ) {}

  async importIntakeData(
    matchedData: MatchedCourseData[]
  ): Promise<ImportResult> {
    const backupId = await this.backupService.createSnapshot();

    try {
      await this.supabaseService.beginTransaction();

      for (const courseData of matchedData) {
        await this.updateCourseIntakes(courseData);
      }

      await this.supabaseService.commitTransaction();
      await this.backupService.markSuccessful(backupId);

      return this.generateSuccessReport();
    } catch (error) {
      await this.supabaseService.rollbackTransaction();
      await this.backupService.restoreSnapshot(backupId);
      throw new ImportError("Import failed and rolled back", error);
    }
  }

  private async updateCourseIntakes(courseData: MatchedCourseData) {
    // Delete existing intakes for this course-location combination
    // Insert new intake dates
    // Validate data integrity after each operation
  }
}
```

## Configuration Management

### Location Mappings Configuration

```json
{
  "locationMappings": {
    "SYD": "Sydney",
    "MELB": "Melbourne",
    "MEL": "Melbourne",
    "BNE": "Brisbane",
    "BSB": "Brisbane",
    "HOB": "Hobart",
    "HOBA": "Hobart",
    "PER": "Perth",
    "ADL": "Adelaide"
  },
  "locationAliases": {
    "Sydney": ["SYD", "SYDNEY", "NSW"],
    "Melbourne": ["MELB", "MEL", "VIC", "MELBOURNE"],
    "Brisbane": ["BNE", "BSB", "QLD", "BRISBANE"],
    "Hobart": ["HOB", "HOBA", "TAS", "HOBART"]
  }
}
```

### Course Pattern Configuration

```json
{
  "vetCodePatterns": [
    "([A-Z]{3}\\d{5})", // Standard: BSB80120
    "([A-Z]{3}\\d{4}[A-Z]?)", // Variant: CHC30121
    "([A-Z]{2,4}\\d{4,5})" // Flexible: ICT40120
  ],
  "courseNamePatterns": {
    "locationExtraction": [
      "\\(([^)]+)\\)$", // Extract from parentheses at end
      "\\(([^)]+)\\)\\s*-", // Extract from parentheses before dash
      "-\\s*([A-Z, &]+)$" // Extract after dash at end
    ]
  },
  "providerAliases": {
    "AIBT": ["Australian Institute of Business and Technology"],
    "AVTA": ["Australian Vocational Training Academy"],
    "Reach College": ["Reach", "Reach Community College"]
  }
}
```

## Data Flow Architecture

### Import Process Flow

```mermaid
graph TD
    A[JSON File Input] --> B[Parse & Validate JSON]
    B --> C[Extract VET Codes & Locations]
    C --> D[Match Courses to Database]
    D --> E[Create Backup Snapshot]
    E --> F[Begin Database Transaction]
    F --> G[Update Intake Dates]
    G --> H{All Updates Successful?}
    H -->|Yes| I[Commit Transaction]
    H -->|No| J[Rollback Transaction]
    J --> K[Restore from Backup]
    I --> L[Generate Success Report]
    K --> M[Generate Error Report]
    L --> N[Cleanup Old Backups]
    M --> N
```

### Matching Algorithm Flow

```mermaid
graph TD
    A[Course Name Input] --> B[Extract VET Code]
    B --> C[Extract Location Codes]
    C --> D[Query Database by VET Code + Provider]
    D --> E{Exact Match Found?}
    E -->|Yes| F[Map Location Codes]
    E -->|No| G[Try Fuzzy Name Matching]
    G --> H{Fuzzy Match Found?}
    H -->|Yes| F
    H -->|No| I[Flag for Manual Review]
    F --> J[Validate Location Mappings]
    J --> K[Return Match Result]
    I --> K
```

## Database Integration

### Existing Schema Integration

The system works with your existing Supabase schema:

```sql
-- Existing tables (preserved as-is)
providers (id, name, created_at)
faculties (id, provider_id, name, created_at)
courses (id, faculty_id, course_name, vet_code, cricos_code, created_at)
locations (id, name, code, created_at)
intakes (id, course_id, location_id, intake_date, created_at)

-- New utility tables for sync system
sync_logs (
  id UUID PRIMARY KEY,
  operation_type VARCHAR(50),
  status VARCHAR(20),
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  records_processed INTEGER,
  records_successful INTEGER,
  records_failed INTEGER,
  error_details JSONB,
  backup_id UUID
);

sync_backups (
  id UUID PRIMARY KEY,
  created_at TIMESTAMP,
  backup_data JSONB,
  status VARCHAR(20),
  restored_at TIMESTAMP
);
```

### Service Layer Integration

```typescript
// Extend existing SupabaseService with sync capabilities
export class SupabaseService {
  // ... existing methods ...

  // New sync-specific methods
  async createIntakeBackup(): Promise<string> {
    // Create snapshot of current intake data
  }

  async restoreIntakeBackup(backupId: string): Promise<void> {
    // Restore intake data from backup
  }

  async replaceIntakesForCourse(
    courseId: string,
    locationId: string,
    intakeDates: string[]
  ): Promise<void> {
    // Replace all intakes for specific course-location combination
  }

  async logSyncOperation(operation: SyncOperation): Promise<void> {
    // Log sync operations for audit trail
  }
}
```

## Error Handling and Recovery

### Error Classification

```typescript
enum ErrorType {
  PARSING_ERROR = "parsing_error",
  MATCHING_ERROR = "matching_error",
  DATABASE_ERROR = "database_error",
  VALIDATION_ERROR = "validation_error",
  CONFIGURATION_ERROR = "configuration_error",
}

class SyncError extends Error {
  constructor(
    public type: ErrorType,
    public message: string,
    public context: any = {},
    public recoverable: boolean = true
  ) {
    super(message);
  }
}
```

### Recovery Strategies

1. **Parsing Errors**: Skip invalid records, continue processing, report at end
2. **Matching Errors**: Flag for manual review, continue with other records
3. **Database Errors**: Rollback transaction, restore backup, report failure
4. **Validation Errors**: Skip invalid data, log warnings, continue processing
5. **Configuration Errors**: Fail fast with clear guidance for resolution

## Performance Optimization

### Batch Processing Strategy

```typescript
class BatchProcessor {
  private readonly BATCH_SIZE = 100;

  async processBatches<T>(
    items: T[],
    processor: (batch: T[]) => Promise<void>
  ): Promise<void> {
    for (let i = 0; i < items.length; i += this.BATCH_SIZE) {
      const batch = items.slice(i, i + this.BATCH_SIZE);
      await processor(batch);

      // Progress reporting
      this.logger.info(`Processed ${i + batch.length}/${items.length} records`);
    }
  }
}
```

### Caching Strategy

- **Course Cache**: Cache course lookups by VET code for session duration
- **Location Cache**: Cache location mappings to avoid repeated queries
- **Provider Cache**: Cache provider information for faster matching

## Monitoring and Reporting

### Import Report Structure

```typescript
interface ImportReport {
  summary: {
    totalRecords: number;
    successfulRecords: number;
    failedRecords: number;
    skippedRecords: number;
    duration: number;
    timestamp: Date;
  };
  details: {
    matchedCourses: MatchedCourse[];
    unmatchedCourses: UnmatchedCourse[];
    errors: ErrorDetail[];
    warnings: WarningDetail[];
  };
  statistics: {
    coursesByProvider: Record<string, number>;
    intakesByLocation: Record<string, number>;
    dateRange: { earliest: Date; latest: Date };
  };
}
```

### Health Check System

```typescript
class HealthChecker {
  async performHealthCheck(): Promise<HealthReport> {
    return {
      database: await this.checkDatabaseConnection(),
      configuration: await this.validateConfiguration(),
      dataIntegrity: await this.checkDataIntegrity(),
      backupSystem: await this.checkBackupSystem(),
    };
  }
}
```

## Security Considerations

### Data Protection

- **Input Sanitization**: Validate all JSON input to prevent injection attacks
- **Database Permissions**: Use least-privilege database access
- **Backup Encryption**: Encrypt backup data at rest
- **Audit Logging**: Log all operations for security auditing

### Access Control

- **Environment Variables**: Store sensitive configuration in environment variables
- **Connection Security**: Use SSL/TLS for all database connections
- **Rate Limiting**: Implement rate limiting for API calls to prevent abuse

## Testing Strategy

### Test Coverage Requirements

- **Unit Tests**: 90%+ coverage for core logic components
- **Integration Tests**: Full database integration scenarios
- **End-to-End Tests**: Complete import workflows with real data
- **Performance Tests**: Large dataset processing validation
- **Error Scenario Tests**: Comprehensive failure mode testing

### Test Data Management

```typescript
// Test fixtures for consistent testing
const testFixtures = {
  validJsonData: require("./fixtures/valid-intake-data.json"),
  invalidJsonData: require("./fixtures/invalid-intake-data.json"),
  mockDatabaseState: require("./fixtures/mock-database.json"),
  expectedMappings: require("./fixtures/expected-mappings.json"),
};
```

## Deployment and Operations

### CLI Interface Design

```bash
# Basic import operation
npm run sync -- --file intake-dates.json

# Import with custom configuration
npm run sync -- --file intake-dates.json --config custom-config.json

# Dry run mode (no database changes)
npm run sync -- --file intake-dates.json --dry-run

# Interactive mode for complex scenarios
npm run sync -- --interactive

# Health check
npm run health-check

# Rollback to previous state
npm run rollback -- --backup-id <backup-id>

# Generate mapping report
npm run analyze -- --file intake-dates.json
```

### Maintenance Operations

- **Automated Backups**: Scheduled cleanup of old backup snapshots
- **Log Rotation**: Automatic log file management and archival
- **Configuration Validation**: Regular validation of mapping configurations
- **Performance Monitoring**: Track import performance over time

This design provides a robust, maintainable, and scalable solution that integrates seamlessly with your existing system while providing the durability and reliability you require.

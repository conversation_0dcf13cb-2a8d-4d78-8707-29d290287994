# Requirements Document

## Introduction

This system will create a simple JSON import utility that updates intake dates in an existing Supabase database. The utility will parse the intake-dates.json file, match courses by name/code to existing database records, extract location information from course names, and update only the intake dates while preserving all existing course metadata and database structure.

## Requirements

### Requirement 1: Intelligent Course Matching and Mapping

**User Story:** As a system administrator, I want to run a robust import script that intelligently matches JSON courses to database records, so that intake dates are updated accurately without data corruption.

#### Acceptance Criteria

1. WHEN I run the import script with intake-dates.json THEN the system SHALL parse the JSON structure (College → Course → Intake Dates) with comprehensive error handling
2. WHEN processing course names THEN the system SHALL extract VET codes using regex patterns (e.g., "BSB80120", "CHC30121") and match them to existing courses
3. WHEN processing course names THEN the system SHALL extract location codes using pattern matching (e.g., "SYD, BSB & HOB", "Sydney", "Melbourne") and map them to location records
4. WHEN a VET code matches multiple courses THEN the system SHALL use provider name + VET code combination for unique matching
5. WHEN location codes are ambiguous THEN the system SHALL use a mapping table (e.g., "SYD" → "Sydney", "BSB" → "Brisbane", "HOB" → "Hobart")
6. WHEN course matching fails THEN the system SHALL log detailed information (provider, course name, extracted codes) for manual review
7. WHEN the system encounters data inconsistencies THEN the system SHALL continue processing and report all issues at the end

### Requirement 2: Intake Date Synchronization and Change Management

**User Story:** As a system administrator, I want the system to intelligently sync intake dates for existing courses and handle data changes, so that my database stays current with the JSON source while preserving existing course structure.

#### Acceptance Criteria

1. WHEN a course exists in both JSON and database THEN the system SHALL synchronize intake dates by adding new dates and removing outdated ones
2. WHEN a course appears in JSON but not in database THEN the system SHALL flag it as "unmapped" and require manual course creation and faculty assignment
3. WHEN a course exists in database but not in current JSON THEN the system SHALL mark its intake dates as inactive (preserving historical data)
4. WHEN intake dates are modified for existing courses THEN the system SHALL update only the changed dates without affecting course metadata
5. WHEN provider names in JSON don't exactly match database provider names THEN the system SHALL use fuzzy matching and provide mapping suggestions
6. WHEN location codes in course names change THEN the system SHALL update the location mappings for those intake records

### Requirement 3: Powerful Search and Filter Capabilities

**User Story:** As a user of the deferment calculator, I want to quickly find courses using search and filters, so that I can easily locate the specific course I need among hundreds of options.

#### Acceptance Criteria

1. WHEN I search for course names THEN the system SHALL provide fuzzy text matching across course titles
2. WHEN I search for course codes THEN the system SHALL match exact and partial course codes (e.g., "BSB80120")
3. WHEN I filter by college THEN the system SHALL show only courses from selected colleges
4. WHEN I filter by course level THEN the system SHALL categorize courses (Certificate, Diploma, Advanced Diploma, Graduate Diploma)
5. WHEN I search THEN the system SHALL return results ranked by relevance with highlighting of matched terms
6. WHEN I apply multiple filters THEN the system SHALL combine them with AND logic
7. WHEN search results are displayed THEN the system SHALL show course name, college, and available intake dates

### Requirement 4: Intake Date Integration for Deferment Calculator

**User Story:** As a student using the deferment calculator, I want to see available intake dates for my selected course, so that I can calculate deferment periods accurately without manual date entry.

#### Acceptance Criteria

1. WHEN I select a course in the deferment calculator THEN the system SHALL display all available intake dates for that course
2. WHEN intake dates are shown THEN the system SHALL sort them chronologically starting from the current date
3. WHEN I select an intake date THEN the system SHALL use it for deferment calculations
4. WHEN there are no future intake dates THEN the system SHALL show a message indicating no upcoming intakes
5. WHEN intake dates are filtered THEN the system SHALL only show dates that are in the future (not past dates)

### Requirement 5: Data Sync Monitoring and Logging

**User Story:** As a system administrator, I want to monitor sync operations and track data changes, so that I can ensure data integrity and troubleshoot issues.

#### Acceptance Criteria

1. WHEN a sync operation runs THEN the system SHALL log the start time, duration, and completion status
2. WHEN data changes occur THEN the system SHALL record what changed (added/updated/deactivated records)
3. WHEN errors occur during sync THEN the system SHALL log detailed error messages with context
4. WHEN sync completes THEN the system SHALL provide a summary report showing statistics
5. WHEN I view sync history THEN the system SHALL show previous sync operations with their results
6. WHEN data conflicts are detected THEN the system SHALL log the conflicts and resolution actions taken

### Requirement 6: Performance and Scalability

**User Story:** As a user, I want the search and sync operations to be fast and responsive, so that the system remains usable as the course catalog grows.

#### Acceptance Criteria

1. WHEN searching courses THEN the system SHALL return results within 500ms for typical queries
2. WHEN syncing JSON data THEN the system SHALL process 1000+ courses within 30 seconds
3. WHEN the database grows THEN the system SHALL maintain search performance through proper indexing
4. WHEN multiple users search simultaneously THEN the system SHALL handle concurrent requests without degradation
5. WHEN large JSON files are processed THEN the system SHALL use batch operations to avoid memory issues

### Requirement 7: Data Validation and Quality

**User Story:** As a system administrator, I want the system to validate data quality during import, so that invalid or inconsistent data doesn't corrupt the database.

#### Acceptance Criteria

1. WHEN processing JSON data THEN the system SHALL validate that required fields are present (collegeName, courseName, date)
2. WHEN intake dates are processed THEN the system SHALL validate date formats and reject invalid dates (like "1900-01-00")
3. WHEN duplicate courses are detected within the same college THEN the system SHALL merge their intake dates
4. WHEN course names contain location codes THEN the system SHALL extract and normalize location information
5. WHEN data inconsistencies are found THEN the system SHALL report them without stopping the import process

### Requirement 8: Transaction Safety and Rollback

**User Story:** As a system administrator, I want the import process to be safe and recoverable, so that I can rollback changes if something goes wrong.

#### Acceptance Criteria

1. WHEN starting an import operation THEN the system SHALL create a backup snapshot of current intake data
2. WHEN the import process fails THEN the system SHALL automatically rollback all changes made during that session
3. WHEN I need to manually rollback THEN the system SHALL provide a rollback command that restores the previous state
4. WHEN performing database operations THEN the system SHALL use transactions to ensure data consistency
5. WHEN the import completes successfully THEN the system SHALL commit all changes atomically
6. WHEN multiple import operations run THEN the system SHALL prevent concurrent imports to avoid conflicts

### Requirement 9: Configuration and Maintenance

**User Story:** As a system administrator, I want to configure the import behavior and maintain the system easily, so that it remains reliable over time.

#### Acceptance Criteria

1. WHEN setting up the system THEN the system SHALL provide a configuration file for location mappings, course patterns, and provider mappings
2. WHEN location codes change THEN the system SHALL allow updating the location mapping table without code changes
3. WHEN new course patterns emerge THEN the system SHALL support adding new VET code extraction patterns
4. WHEN provider names change THEN the system SHALL provide a provider mapping configuration
5. WHEN running diagnostics THEN the system SHALL provide health check commands to verify database connectivity and data integrity
6. WHEN scheduling imports THEN the system SHALL support both manual execution and scheduled automation

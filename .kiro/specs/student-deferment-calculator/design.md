# Design Document

## Overview

The Student Deferment Calculator System is a Next.js 13+ web application using the App Router architecture. The system replaces complex Excel-based processes with an intuitive web interface that handles course deferment calculations and intake date management. The application features a dark theme UI with Tailwind CSS, server-side rendering for performance, and client-side interactivity for calculations.

## Architecture

### Technology Stack
- **Framework**: Next.js 13+ with App Router
- **Language**: TypeScript for type safety
- **Styling**: Tailwind CSS with custom component classes
- **Date Handling**: date-fns library for reliable date calculations
- **Icons**: Lucide React for consistent iconography
- **Data Source**: Static JSON file (intake-dates.json) in project root

### Application Structure
```
/
├── intake-dates.json (data source)
├── app/
│   ├── layout.tsx (root layout with sidebar)
│   ├── page.tsx (redirect to calculator)
│   ├── globals.css (Tailwind + custom styles)
│   ├── deferment-calculator/
│   │   ├── page.tsx (calculator page)
│   │   ├── loading.tsx (loading state)
│   │   └── error.tsx (error boundary)
│   └── intake-dates/
│       ├── page.tsx (intake viewer page)
│       ├── loading.tsx (loading state)
│       └── error.tsx (error boundary)
├── components/
│   ├── Sidebar.tsx (navigation)
│   ├── DefermentCalculator.tsx (main calculator)
│   ├── IntakeDatesViewer.tsx (intake display)
│   ├── CourseCard.tsx (course display component)
│   └── Icons.tsx (icon components)
├── lib/
│   ├── types.ts (TypeScript interfaces)
│   ├── utils/
│   │   └── dateUtils.ts (date calculation functions)
│   └── data/
│       └── courseData.ts (data loading and parsing)
└── public/ (static assets)
```

### Rendering Strategy
- **Server Components**: Used for data fetching (intake dates viewer, initial data loading)
- **Client Components**: Used for interactive features (calculator with state management)
- **Hybrid Approach**: Server-side data fetching with client-side interactivity

## Components and Interfaces

### Core Data Types
```typescript
interface College {
  collegeName: string;
  courses: Course[];
}

interface Course {
  courseName: string;
  intakes: IntakeDate[];
}

interface IntakeDate {
  date: string;
}

interface StudentData {
  studentNumber: string;
  courses: CourseData[];
}

interface CourseData {
  courseName: string;
  collegeName: string;
  defermentStartDate?: Date;
  actualEndDate?: Date;
  resumptionDate?: Date;
  adjustedResumptionDate?: Date;
  defermentDuration?: number;
  newCourseEndDate?: Date;
  originalCourseDuration?: number;
}
```

### Component Architecture

#### Sidebar Component
- **Type**: Client Component
- **Responsibilities**: Navigation between calculator and intake viewer
- **State**: Current pathname for active link highlighting
- **Styling**: Fixed width sidebar with dark theme

#### DefermentCalculator Component
- **Type**: Client Component
- **Responsibilities**: 
  - Student data input form
  - Date calculations and validation
  - Course sequence management (up to 4 courses)
  - Real-time calculation updates
- **State Management**: React hooks for form data and calculated results
- **Key Features**:
  - Searchable course dropdown
  - Date picker with Monday/Sunday validation
  - Dynamic course card generation
  - Automatic date adjustments

#### IntakeDatesViewer Component
- **Type**: Server Component with Client-side filtering
- **Responsibilities**:
  - Display all intake dates grouped by college/course
  - Client-side filtering and search
  - Date formatting and highlighting
- **Data Loading**: Server-side data fetching with client-side interactivity

#### CourseCard Component
- **Type**: Client Component
- **Responsibilities**: Display individual course information with calculated dates
- **Props**: Course data, removal handler, position in sequence

### Business Logic Implementation

#### Date Calculation Engine
```typescript
// Core calculation formula implementation
export function calculateDefermentDetails(
  defermentStartDate: Date,
  actualEndDate: Date,
  resumptionDate: Date,
  originalCourseDuration: number
): {
  defermentDuration: number;
  adjustedResumptionDate: Date;
  newCourseEndDate: Date;
} {
  // 1. Calculate deferment duration in weeks
  const defermentDuration = differenceInWeeks(actualEndDate, defermentStartDate);
  
  // 2. Adjust resumption date to next Monday
  const adjustedResumptionDate = adjustToNextMonday(resumptionDate);
  
  // 3. Calculate new course end date (add duration, adjust to Sunday)
  const newCourseEndDate = adjustToEndOfWeekSunday(
    addWeeks(adjustedResumptionDate, originalCourseDuration)
  );
  
  return { defermentDuration, adjustedResumptionDate, newCourseEndDate };
}
```

#### Sequential Course Scheduling
- **Logic**: Each subsequent course starts on the next available intake date after the previous course ends
- **Validation**: All start dates must be Mondays, all end dates must be Sundays
- **Intake Matching**: System searches available intake dates for the selected college/course combination

## Data Models

### Data Loading Strategy
```typescript
// Server-side data loading
export async function getIntakeData(): Promise<College[]> {
  const rawData = await import('@/intake-dates.json');
  
  // Parse and validate data
  const validatedData = rawData.default.map(college => ({
    ...college,
    courses: college.courses.map(course => ({
      ...course,
      intakes: course.intakes.filter(intake => 
        isValidDate(intake.date) && intake.date !== "1900-01-00"
      )
    }))
  }));
  
  return validatedData;
}
```

### State Management Pattern
- **Local State**: React hooks (useState, useEffect) for component-level state
- **Form State**: Controlled components with validation
- **Derived State**: Calculated values computed from input data
- **No Global State**: Application complexity doesn't warrant Redux/Zustand

### Data Validation
- **Date Validation**: Reject invalid dates like "1900-01-00"
- **Course Duration**: Extract duration from course names or use defaults
- **Input Validation**: Form validation with clear error messages
- **Type Safety**: TypeScript interfaces for all data structures

## Error Handling

### Error Boundary Strategy
```typescript
// App-level error boundaries for each route
export default function Error({
  error,
  reset,
}: {
  error: Error;
  reset: () => void;
}) {
  return (
    <div className="error-container">
      <h2>Something went wrong!</h2>
      <p>{error.message}</p>
      <button onClick={() => reset()}>Try again</button>
    </div>
  );
}
```

### Error Types and Handling
- **Data Loading Errors**: Graceful fallbacks when intake data fails to load
- **Calculation Errors**: Validation and error messages for invalid date combinations
- **Form Validation**: Real-time validation with specific error descriptions
- **Network Errors**: Retry mechanisms and user-friendly error messages

### User Feedback
- **Loading States**: Skeleton loaders and progress indicators
- **Success States**: Visual confirmation of successful calculations
- **Error States**: Clear error messages with actionable guidance
- **Validation States**: Real-time form validation feedback

## Testing Strategy

### Unit Testing
- **Date Utilities**: Comprehensive tests for all date calculation functions
- **Business Logic**: Test deferment calculations with various scenarios
- **Component Logic**: Test component state management and user interactions
- **Data Parsing**: Test intake data loading and validation

### Integration Testing
- **Form Workflows**: Test complete user workflows from input to results
- **Navigation**: Test sidebar navigation and route transitions
- **Data Flow**: Test data flow from input through calculations to display

### Edge Case Testing
- **Invalid Dates**: Test handling of malformed date data
- **Boundary Conditions**: Test with edge dates (weekends, holidays)
- **Maximum Courses**: Test with 4 sequential courses
- **Empty Data**: Test behavior with missing or empty intake data

### Performance Testing
- **Large Datasets**: Test with extensive intake date collections
- **Calculation Performance**: Ensure fast response times for complex calculations
- **Rendering Performance**: Test smooth UI updates during calculations

## User Experience Design

### Visual Design System
```css
/* Custom Tailwind component classes */
@layer components {
  .input-field {
    @apply bg-gray-800 border-gray-700 text-white rounded-lg px-4 py-2 
           focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }
  
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 
           rounded-lg transition-colors duration-200 font-medium;
  }
  
  .card {
    @apply bg-gray-800 rounded-lg p-6 border border-gray-700 
           shadow-lg hover:shadow-xl transition-shadow duration-200;
  }
}
```

### Responsive Design
- **Mobile First**: Design starts with mobile constraints
- **Breakpoint Strategy**: Tailwind's responsive utilities for different screen sizes
- **Touch Targets**: Minimum 44px touch targets for mobile usability
- **Navigation**: Collapsible sidebar for mobile devices

### Accessibility
- **Keyboard Navigation**: Full keyboard accessibility for all interactive elements
- **Screen Readers**: Proper ARIA labels and semantic HTML
- **Color Contrast**: WCAG AA compliant color combinations
- **Focus Management**: Clear focus indicators and logical tab order

### Performance Optimization
- **Code Splitting**: Automatic code splitting with Next.js App Router
- **Image Optimization**: Next.js Image component for any visual assets
- **Bundle Optimization**: Tree shaking and dead code elimination
- **Caching Strategy**: Appropriate cache headers for static assets

## Security Considerations

### Input Validation
- **Client-side Validation**: Immediate feedback for user experience
- **Server-side Validation**: Security validation for all inputs
- **XSS Prevention**: Proper input sanitization and output encoding
- **Type Safety**: TypeScript for compile-time type checking

### Data Security
- **No Sensitive Data**: Application handles only academic scheduling data
- **Local Processing**: All calculations performed client-side
- **Static Data**: Intake dates served as static JSON (no database)
- **No Authentication**: Public application with no user accounts

## Deployment and Infrastructure

### Build Configuration
- **Static Export**: Next.js static export for simple deployment
- **Environment Variables**: Configuration for different environments
- **Asset Optimization**: Automatic optimization of CSS, JS, and images
- **Bundle Analysis**: Tools for monitoring bundle size

### Deployment Strategy
- **Static Hosting**: Suitable for Vercel, Netlify, or similar platforms
- **CDN Distribution**: Global content delivery for performance
- **Monitoring**: Error tracking and performance monitoring
- **Analytics**: Usage tracking for feature optimization

### Maintenance Considerations
- **Data Updates**: Process for updating intake-dates.json
- **Version Control**: Git workflow for collaborative development
- **Documentation**: Comprehensive README and code documentation
- **Backup Strategy**: Version control serves as backup for code and data
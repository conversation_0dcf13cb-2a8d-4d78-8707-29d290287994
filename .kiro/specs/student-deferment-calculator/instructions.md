Based on the chat history and requirements, here are comprehensive instructions for building the Student Deferment Calculator System using Next.js:

## Instructions for Building Student Deferment Calculator System with Next.js

### Project Overview
Build a Next.js web application with two main features accessible through a sidebar navigation:
1. **Student Deferment Calculator** - For calculating course deferrals and new dates
2. **Intake Dates Viewer** - For displaying available course intake dates

The `intake-dates.json` file should be placed in the project root directory.

### Core Business Logic Understanding

#### Deferment Calculator Logic:
- Students can defer their courses and the system calculates:
  - Deferment duration (in weeks)
  - New course end dates
  - Sequential course start dates for up to 4 courses
- **Critical Rules**:
  - All course START dates must be on a MONDAY
  - All course END dates must be on a SUNDAY
  - When a student resumes after deferment, their resumption date is automatically adjusted to the next Monday
  - The system adds the remaining course duration to calculate the new end date (which lands on a Sunday)

#### Date Calculation Formula:
```javascript
// 1. Calculate deferment duration
defermentDuration = (actualEndDate - defermentStartDate) / 7 // in weeks

// 2. For resumption date - adjust to next Monday
adjustedResumptionDate = adjustToNextMonday(selectedResumptionDate)

// 3. Calculate new course end date
// Add the original course duration to the adjusted resumption date
// Then adjust to end on Sunday
newCourseEndDate = adjustToEndOfWeekSunday(adjustedResumptionDate + courseDuration)

// 4. For subsequent courses
// Find next available intake date (Monday) after previous course ends (Sunday)
nextCourseStartDate = findNextIntakeDate(previousCourseEndDate, courseIntakes)
```

### Technical Requirements

#### 1. Project Structure:
```
/
├── intake-dates.json (in project root)
├── app/
│   ├── layout.tsx
│   ├── page.tsx
│   ├── globals.css
│   ├── deferment-calculator/
│   │   └── page.tsx
│   └── intake-dates/
│       └── page.tsx
├── components/
│   ├── Sidebar.tsx
│   ├── DefermentCalculator.tsx
│   ├── IntakeDatesViewer.tsx
│   ├── CourseCard.tsx
│   └── Icons.tsx
├── lib/
│   ├── types.ts
│   ├── utils/
│   │   └── dateUtils.ts
│   └── data/
│       └── courseData.ts
└── public/
```

#### 2. Next.js Setup:
```bash
# Use App Router (Next.js 13+)
# Install dependencies
npm install next react react-dom typescript @types/react @types/node
npm install tailwindcss @tailwindcss/forms
npm install date-fns lucide-react
```

#### 3. Data Loading Strategy:
```typescript
// lib/data/courseData.ts
import intakeData from '@/intake-dates.json';

export interface College {
  collegeName: string;
  courses: Course[];
}

export interface Course {
  courseName: string;
  intakes: IntakeDate[];
}

export interface IntakeDate {
  date: string;
}

// Parse and validate the JSON data
export function getIntakeData(): College[] {
  // Parse the intake-dates.json file
  // Handle invalid dates like "1900-01-00"
  // Return structured data
}

// Server-side data fetching for intake dates page
export async function getCollegesAndCourses() {
  const data = getIntakeData();
  return data;
}
```

#### 4. App Router Implementation:

**app/layout.tsx:**
```typescript
export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        <div className="flex h-screen bg-gray-900">
          <Sidebar />
          <main className="flex-1 overflow-auto">
            {children}
          </main>
        </div>
      </body>
    </html>
  )
}
```

**app/page.tsx:**
```typescript
// Redirect to deferment calculator by default
import { redirect } from 'next/navigation';

export default function HomePage() {
  redirect('/deferment-calculator');
}
```

#### 5. Component Specifications:

**Sidebar Component (components/Sidebar.tsx):**
```typescript
'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

export default function Sidebar() {
  const pathname = usePathname();
  
  return (
    <aside className="w-64 bg-gray-800 text-white">
      <nav>
        <Link 
          href="/deferment-calculator"
          className={pathname === '/deferment-calculator' ? 'active' : ''}
        >
          Deferment Calculator
        </Link>
        <Link 
          href="/intake-dates"
          className={pathname === '/intake-dates' ? 'active' : ''}
        >
          Intake Dates
        </Link>
      </nav>
    </aside>
  );
}
```

**DefermentCalculator Component Requirements:**
- Student number input field
- First course details:
  - Course name (searchable dropdown from available courses)
  - College selection (affects available courses)
  - Deferment start date (date picker)
  - Actual current course end date (date picker)
  - Resumption date (date picker) - show hint about Monday adjustment
- Display calculated fields:
  - Deferment duration (weeks)
  - Adjusted resumption date (shows Monday if adjusted)
  - New course end date (shows Sunday)
- Course cards for additional courses (2-4):
  - Auto-populated start date based on previous course end + intake availability
  - Calculated end date
  - Option to remove course

**IntakeDatesViewer Component Requirements:**
- Server-side rendered list of all intake dates
- Filter by college (client-side)
- Search by course name (client-side)
- Group display by college and course
- Show dates in readable format
- Highlight upcoming intakes vs past intakes

#### 6. State Management:
```typescript
// Use React hooks for client components
'use client';

import { useState, useEffect } from 'react';

interface StudentData {
  studentNumber: string;
  courses: CourseData[];
}

interface CourseData {
  courseName: string;
  collegeName: string;
  defermentStartDate?: Date;
  actualEndDate?: Date;
  resumptionDate?: Date;
  adjustedResumptionDate?: Date;
  defermentDuration?: number;
  newCourseEndDate?: Date;
  originalCourseDuration?: number; // in weeks
}
```

#### 7. Utility Functions:
```typescript
// lib/utils/dateUtils.ts
import { startOfWeek, endOfWeek, addDays, differenceInWeeks } from 'date-fns';

export function adjustToNextMonday(date: Date): Date {
  // If date is already Monday, return it
  // Otherwise, return next Monday
}

export function adjustToEndOfWeekSunday(date: Date): Date {
  // Return the Sunday of the week containing the date
}

export function calculateWeeksBetween(start: Date, end: Date): number {
  return differenceInWeeks(end, start);
}

export function findNextAvailableIntake(
  afterDate: Date, 
  intakeDates: string[], 
  collegeName: string,
  courseName: string
): Date | null {
  // Find the next intake date after the given date
  // Return null if no future intakes available
}

// Parse course duration from course name if available
export function extractCourseDuration(courseName: string): number | null {
  const match = courseName.match(/(\d+)\s*weeks?/i);
  return match ? parseInt(match[1]) : null;
}
```

#### 8. Next.js Specific Features to Implement:

**Loading States:**
```typescript
// app/deferment-calculator/loading.tsx
export default function Loading() {
  return <div>Loading calculator...</div>
}
```

**Error Boundaries:**
```typescript
// app/deferment-calculator/error.tsx
'use client';

export default function Error({
  error,
  reset,
}: {
  error: Error;
  reset: () => void;
}) {
  return (
    <div>
      <h2>Something went wrong!</h2>
      <button onClick={() => reset()}>Try again</button>
    </div>
  );
}
```

#### 9. Styling with Tailwind CSS:
```css
/* app/globals.css */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer base {
  body {
    @apply bg-gray-900 text-gray-100;
  }
}

@layer components {
  .input-field {
    @apply bg-gray-800 border-gray-700 text-white rounded-lg px-4 py-2;
  }
  
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors;
  }
  
  .card {
    @apply bg-gray-800 rounded-lg p-6 border border-gray-700;
  }
}
```

### Important Implementation Notes:

1. **Server Components vs Client Components**:
   - Use Server Components for data fetching (intake dates viewer)
   - Use Client Components for interactive features (calculator with state)
   - Mark components with `'use client'` directive when needed

2. **Data Fetching**:
   - Load `intake-dates.json` using Node.js file system in server components
   - Or import directly as JSON module
   - Cache the parsed data to avoid repeated file reads

3. **Performance Optimizations**:
   - Use Next.js Image component if adding logos/images
   - Implement proper loading states
   - Consider using Suspense for data fetching

4. **Type Safety**:
   - Create proper TypeScript interfaces for all data structures
   - Validate JSON data after parsing
   - Handle edge cases with proper error messages

5. **User Experience Enhancements**:
   - Show toast notifications for important actions
   - Add keyboard navigation support
   - Implement form validation with clear error messages
   - Add a "Reset" button to clear all calculations

### Deployment Considerations:
- Ensure `intake-dates.json` is included in the build
- Set up proper error logging
- Consider adding analytics to track usage
- Implement proper meta tags for SEO

The goal is to create a professional, performant Next.js application that replaces the complex Excel process with an intuitive web interface while maintaining calculation accuracy and improving user experience significantly.
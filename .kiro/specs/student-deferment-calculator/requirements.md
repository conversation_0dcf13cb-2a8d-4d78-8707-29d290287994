# Requirements Document

## Introduction

The Student Deferment Calculator System is a Next.js web application designed to replace complex Excel-based processes for calculating course deferrals and managing intake dates. The system provides two main features: a deferment calculator that computes new course dates when students defer their studies, and an intake dates viewer that displays available course start dates across multiple colleges. The application must handle complex business logic including mandatory Monday start dates, Sunday end dates, and sequential course scheduling for up to 4 courses.

## Requirements

### Requirement 1

**User Story:** As a student advisor, I want to calculate deferment durations and new course dates when students defer their studies, so that I can provide accurate scheduling information and plan sequential courses.

#### Acceptance Criteria

1. WHEN a user enters student details (student number, course name, college, deferment start date, actual end date, resumption date) THEN the system SHALL calculate and display the deferment duration in weeks
2. WHEN a resumption date is selected THEN the system SHALL automatically adjust it to the next Monday if it's not already a Monday
3. WHEN calculating new course end dates THEN the system SHALL ensure all end dates fall on a Sunday
4. WHEN a new course end date is calculated THEN the system SHALL add the original course duration to the adjusted resumption date and adjust to end on Sunday
5. WHEN adding subsequent courses THEN the system SHALL automatically find the next available intake date (Monday) after the previous course ends
6. WHEN displaying calculated results THEN the system SHALL show deferment duration, adjusted resumption date, and new course end date with clear visual indicators

### Requirement 2

**User Story:** As a student advisor, I want to manage multiple sequential courses (up to 4) for a deferred student, so that I can plan their complete academic pathway.

#### Acceptance Criteria

1. WHEN a first course is calculated THEN the system SHALL allow adding up to 3 additional courses
2. WHEN adding a subsequent course THEN the system SHALL auto-populate the start date based on the previous course end date and available intake dates
3. WHEN selecting a course for subsequent enrollment THEN the system SHALL filter available courses by college and show searchable dropdown options
4. WHEN calculating subsequent course dates THEN the system SHALL ensure each course starts on a Monday from available intake dates
5. WHEN displaying course cards THEN the system SHALL show course name, start date, calculated end date, and option to remove the course
6. WHEN removing a course THEN the system SHALL recalculate subsequent course dates automatically

### Requirement 3

**User Story:** As a student advisor, I want to view and search available intake dates across all colleges and courses, so that I can identify suitable enrollment opportunities for students.

#### Acceptance Criteria

1. WHEN accessing the intake dates viewer THEN the system SHALL display all available intake dates grouped by college and course
2. WHEN filtering by college THEN the system SHALL show only courses and dates for the selected college
3. WHEN searching by course name THEN the system SHALL filter results to show matching courses across all colleges
4. WHEN displaying intake dates THEN the system SHALL highlight upcoming intakes differently from past intakes
5. WHEN viewing dates THEN the system SHALL show them in a readable format with clear college and course organization
6. WHEN loading intake data THEN the system SHALL handle invalid dates (like "1900-01-00") gracefully without breaking the interface

### Requirement 4

**User Story:** As a user, I want to navigate between the deferment calculator and intake dates viewer through a sidebar navigation, so that I can easily access both features of the application.

#### Acceptance Criteria

1. WHEN accessing the application THEN the system SHALL display a sidebar navigation with links to both main features
2. WHEN clicking on navigation links THEN the system SHALL highlight the active page and navigate to the selected feature
3. WHEN the application loads THEN the system SHALL redirect to the deferment calculator by default
4. WHEN navigating between pages THEN the system SHALL maintain a consistent layout with the sidebar always visible
5. WHEN using the application THEN the system SHALL provide clear visual feedback for the currently active navigation item

### Requirement 5

**User Story:** As a user, I want the application to handle errors gracefully and provide clear feedback, so that I can understand and resolve any issues that occur.

#### Acceptance Criteria

1. WHEN invalid data is entered THEN the system SHALL display clear error messages with specific guidance for correction
2. WHEN date calculations fail THEN the system SHALL show appropriate error messages without breaking the interface
3. WHEN intake data cannot be loaded THEN the system SHALL display a meaningful error message and recovery options
4. WHEN form validation fails THEN the system SHALL highlight problematic fields and provide specific error descriptions
5. WHEN the application encounters unexpected errors THEN the system SHALL show a user-friendly error boundary with options to retry
6. WHEN loading data THEN the system SHALL display appropriate loading states to inform users of ongoing processes

### Requirement 6

**User Story:** As a user, I want the application to be responsive and performant across different devices, so that I can use it effectively on desktop, tablet, and mobile devices.

#### Acceptance Criteria

1. WHEN accessing the application on different screen sizes THEN the system SHALL adapt the layout appropriately for optimal viewing
2. WHEN using touch devices THEN the system SHALL provide appropriate touch targets and interactions for form elements
3. WHEN loading large datasets THEN the system SHALL implement efficient data loading and caching strategies
4. WHEN rendering intake dates THEN the system SHALL handle large amounts of data without performance degradation
5. WHEN using the sidebar navigation on mobile THEN the system SHALL provide an appropriate mobile navigation experience
6. WHEN interacting with date pickers and dropdowns THEN the system SHALL work consistently across different devices and browsers

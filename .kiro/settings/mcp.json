{"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": false, "autoApprove": []}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--acess-token", "********************************************"], "disabled": false, "autoApprove": ["list_tables", "execute_sql", "list_projects", "get_project", "get_project_url", "get_anon_key"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": []}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"], "disabled": false, "autoApprove": ["browser_navigate", "browser_click", "browser_console_messages", "browser_evaluate", "browser_take_screenshot", "browser_type"]}}}
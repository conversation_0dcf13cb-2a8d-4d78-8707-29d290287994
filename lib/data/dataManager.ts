import { College } from '../types';

export interface DataValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export class IntakeDataManager {
  private static instance: IntakeDataManager;
  private colleges: College[] = [];

  private constructor() {}

  static getInstance(): IntakeDataManager {
    if (!IntakeDataManager.instance) {
      IntakeDataManager.instance = new IntakeDataManager();
    }
    return IntakeDataManager.instance;
  }

  /**
   * Load and validate intake data from JSON
   */
  async loadData(filePath: string = '/intake-dates.json'): Promise<DataValidationResult> {
    try {
      const response = await fetch(filePath);
      const data = await response.json();

      const validation = this.validateData(data);
      if (validation.isValid) {
        this.colleges = data;
      }

      return validation;
    } catch (error) {
      return {
        isValid: false,
        errors: [`Failed to load data: ${error}`],
        warnings: []
      };
    }
  }

  /**
   * Validate data structure and content
   */
  validateData(data: any): DataValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if data is an array
    if (!Array.isArray(data)) {
      errors.push('Data must be an array of colleges');
      return { isValid: false, errors, warnings };
    }

    // Validate each college
    data.forEach((college: any, collegeIndex: number) => {
      if (!college.collegeName) {
        errors.push(`College at index ${collegeIndex} missing collegeName`);
      }

      if (!Array.isArray(college.courses)) {
        errors.push(`College "${college.collegeName}" courses must be an array`);
        return;
      }

      // Validate courses
      college.courses.forEach((course: any, courseIndex: number) => {
        if (!course.courseName) {
          errors.push(`Course at index ${courseIndex} in college "${college.collegeName}" missing courseName`);
        }

        if (!Array.isArray(course.intakes)) {
          errors.push(`Course "${course.courseName}" intakes must be an array`);
          return;
        }

        // Validate intake dates
        course.intakes.forEach((intake: any, intakeIndex: number) => {
          if (!intake.date) {
            errors.push(`Intake at index ${intakeIndex} in course "${course.courseName}" missing date`);
          } else {
            const date = new Date(intake.date);
            if (isNaN(date.getTime())) {
              errors.push(`Invalid date "${intake.date}" in course "${course.courseName}"`);
            } else if (date < new Date()) {
              warnings.push(`Past date "${intake.date}" in course "${course.courseName}"`);
            }
          }
        });

        // Check for duplicate dates
        const dates = course.intakes.map((i: any) => i.date);
        const duplicates = dates.filter((date: string, index: number) => dates.indexOf(date) !== index);
        if (duplicates.length > 0) {
          warnings.push(`Duplicate dates in course "${course.courseName}": ${duplicates.join(', ')}`);
        }
      });
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Get all colleges
   */
  getColleges(): College[] {
    return this.colleges;
  }

  /**
   * Get courses for a specific college
   */
  getCoursesByCollege(collegeName: string) {
    const college = this.colleges.find(c => c.collegeName === collegeName);
    return college?.courses || [];
  }

  /**
   * Get statistics about the data
   */
  getDataStats() {
    const totalColleges = this.colleges.length;
    const totalCourses = this.colleges.reduce((sum, college) => sum + college.courses.length, 0);
    const totalIntakes = this.colleges.reduce((sum, college) =>
      sum + college.courses.reduce((courseSum, course) => courseSum + course.intakes.length, 0), 0
    );

    const oldestDate = new Date(Math.min(...this.colleges.flatMap(c =>
      c.courses.flatMap(course => course.intakes.map(i => new Date(i.date).getTime()))
    )));

    const newestDate = new Date(Math.max(...this.colleges.flatMap(c =>
      c.courses.flatMap(course => course.intakes.map(i => new Date(i.date).getTime()))
    )));

    return {
      totalColleges,
      totalCourses,
      totalIntakes,
      dateRange: {
        oldest: oldestDate.toISOString().split('T')[0],
        newest: newestDate.toISOString().split('T')[0]
      }
    };
  }

  /**
   * Export data in different formats
   */
  exportAsCSV(): string {
    const headers = ['College', 'Course', 'Intake Date'];
    const rows = [headers.join(',')];

    this.colleges.forEach(college => {
      college.courses.forEach(course => {
        course.intakes.forEach(intake => {
          rows.push(`"${college.collegeName}","${course.courseName}","${intake.date}"`);
        });
      });
    });

    return rows.join('\n');
  }

  /**
   * Get courses with upcoming intakes (next 90 days)
   */
  getUpcomingIntakes(days: number = 90) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() + days);

    const upcoming: any[] = [];

    this.colleges.forEach(college => {
      college.courses.forEach(course => {
        const upcomingIntakes = course.intakes.filter(intake => {
          const intakeDate = new Date(intake.date);
          return intakeDate >= new Date() && intakeDate <= cutoffDate;
        });

        if (upcomingIntakes.length > 0) {
          upcoming.push({
            college: college.collegeName,
            course: course.courseName,
            intakes: upcomingIntakes
          });
        }
      });
    });

    return upcoming;
  }
}

export default IntakeDataManager;

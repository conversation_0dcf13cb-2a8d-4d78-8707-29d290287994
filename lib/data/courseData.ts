import { supabaseService } from '@/lib/supabase/service';
import type { LegacyCollege } from '@/lib/supabase/client';

// Cache for performance
let cachedData: LegacyCollege[] | null = null;
let cacheTimestamp: number | null = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Load course data from Supabase
 * Returns data in legacy format for backward compatibility
 */
export async function loadCourseData(): Promise<LegacyCollege[]> {
  // Check cache first
  if (cachedData && cacheTimestamp && Date.now() - cacheTimestamp < CACHE_DURATION) {
    return cachedData;
  }

  try {
    console.log('🔄 Loading course data from Supabase...');
    const data = await supabaseService.getLegacyFormat();

    // Update cache
    cachedData = data;
    cacheTimestamp = Date.now();

    console.log(`✅ Loaded ${data.length} colleges from Supabase`);
    return data;
  } catch (error) {
    console.error('❌ Failed to load course data from Supabase:', error);

    // Return fallback empty data to prevent app crashes
    return [];
  }
}

/**
 * Clear the cache to force fresh data fetch
 */
export function clearCache(): void {
  cachedData = null;
  cacheTimestamp = null;
}

/**
 * Get statistics about the data
 */
export async function getDataStats() {
  try {
    return await supabaseService.getStats();
  } catch (error) {
    console.error('Error fetching data stats:', error);
    return { providers: 0, faculties: 0, courses: 0, locations: 0, intakes: 0 };
  }
}

/**
 * Search courses across all providers
 */
export async function searchCourses(searchTerm: string) {
  try {
    return await supabaseService.searchCourses(searchTerm);
  } catch (error) {
    console.error('Error searching courses:', error);
    return [];
  }
}

/**
 * Get all locations for filtering/selection
 */
export async function getLocations() {
  try {
    return await supabaseService.getAllLocations();
  } catch (error) {
    console.error('Error fetching locations:', error);
    return [];
  }
}

/**
 * Get all providers (colleges) for selection
 */
export async function getProviders() {
  try {
    return await supabaseService.getAllProviders();
  } catch (error) {
    console.error('Error fetching providers:', error);
    return [];
  }
}

/**
 * Get courses by provider for dynamic filtering
 */
export async function getCoursesByProvider(providerId: string) {
  try {
    return await supabaseService.getCourseLocationsByProvider(providerId);
  } catch (error) {
    console.error('Error fetching courses by provider:', error);
    return [];
  }
}

/**
 * Get intake dates for a specific course
 */
export async function getIntakesByCourse(courseId: string) {
  try {
    return await supabaseService.getIntakesByCourse(courseId);
  } catch (error) {
    console.error('Error fetching intakes by course:', error);
    return [];
  }
}

/**
 * Get next available intakes after a specific date
 */
export async function getNextAvailableIntakes(courseId: string, afterDate: string) {
  try {
    return await supabaseService.getNextAvailableIntakes(courseId, afterDate);
  } catch (error) {
    console.error('Error fetching next available intakes:', error);
    return [];
  }
}

import {
  startOfWeek,
  endOfWeek,
  addDays,
  addWeeks,
  differenceInWeeks,
  differenceInDays,
  getDay,
  parseISO,
  format,
  isAfter,
  isBefore
} from 'date-fns';

// General purpose rounding function
export function round(value: number): number {
  return Math.round(value);
}

export function daysBetween(startDate: Date, endDate: Date): number {
  return differenceInDays(endDate, startDate);
}

// Adjust date to the nearest Monday (previous or next)
export function adjustToMonday(date: Date, direction: 'next' | 'preceding' = 'next'): Date {
  const dayOfWeek = getDay(date); // 0 = Sunday, 1 = Monday, etc.

  if (dayOfWeek === 1) {
    return date; // Already Monday
  }

  if (direction === 'next') {
    const daysToAdd = dayOfWeek === 0 ? 1 : 8 - dayOfWeek;
    return addDays(date, daysToAdd);
  } else { // 'preceding'
    const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
    return addDays(date, -daysToSubtract);
  }
}


// Adjust date to end of week (Sunday)
export function adjustToEndOfWeekSunday(date: Date): Date {
  return endOfWeek(date, { weekStartsOn: 1 }); // Week starts on Monday
}

// Calculate weeks between two dates
export function calculateWeeksBetween(startDate: Date, endDate: Date): number {
  return differenceInWeeks(endDate, startDate);
}

// Calculate remaining course duration in weeks
export function calculateRemainingDuration(
  originalStartDate: Date,
  defermentStartDate: Date,
  totalDuration: number
): number {
  const weeksCompleted = calculateWeeksBetween(originalStartDate, defermentStartDate);
  return totalDuration - weeksCompleted;
}

// Calculate complete deferment details
export function calculateDefermentDetails(
  defermentStartDate: Date,
  actualEndDate: Date,
  resumptionDate: Date,
  originalDuration: number
): {
  defermentDuration: number;
  adjustedResumptionDate: Date;
  newCourseEndDate: Date;
} {
  // 1. Calculate deferment duration in weeks
  const defermentDuration = calculateWeeksBetween(defermentStartDate, actualEndDate);

  // 2. Adjust resumption date to next Monday if it's not already a Monday
  const adjustedResumptionDate = isMonday(resumptionDate) ? resumptionDate : adjustToMonday(resumptionDate);

  // 3. Calculate remaining duration
  const remainingDuration = originalDuration - defermentDuration;

  // 4. Calculate new course end date
  const newCourseEndDate = adjustToEndOfWeekSunday(
    addWeeks(adjustedResumptionDate, remainingDuration)
  );

  return {
    defermentDuration,
    adjustedResumptionDate,
    newCourseEndDate,
  };
}

// Format date for display
export function formatDateForDisplay(date: Date): string {
  return format(date, 'PPP'); // e.g., "April 29, 2021"
}

// Format date for input fields
export function formatDateForInput(date: Date): string {
  return format(date, 'yyyy-MM-dd');
}

// Check if a date is a Monday
export function isMonday(date: Date): boolean {
  return getDay(date) === 1;
}

// Check if a date is a Sunday
export function isSunday(date: Date): boolean {
  return getDay(date) === 0;
}

// Adjust date to next Monday (if not already Monday)
export function adjustToNextMonday(date: Date): Date {
  return adjustToMonday(date, 'next');
}

// Find next available intake date after a given date
export function findNextAvailableIntake(
  afterDate: Date,
  intakeDates: string[],
  courseName: string,
  collegeName: string
): Date | null {
  // Create a date at the start of the day for comparison
  const compareDate = new Date(afterDate);
  compareDate.setHours(0, 0, 0, 0);

  const sortedDates = intakeDates
    .map(dateStr => parseISO(dateStr))
    .filter(date => isAfter(date, compareDate))
    .sort((a, b) => a.getTime() - b.getTime());

  if (sortedDates.length === 0) {
    return null;
  }

  // Return the first available date adjusted to next Monday
  return adjustToNextMonday(sortedDates[0]);
}

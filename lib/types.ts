// Core data types for the Student Deferment Calculator

export interface College {
  collegeName: string;
  courses: Course[];
}

export interface Course {
  courseName: string;
  intakes: IntakeDate[];
}

export interface IntakeDate {
  date: string; // ISO format: YYYY-MM-DD
}

// Student and course management types
export interface StudentData {
  studentNumber: string;
  courses: CourseData[];
}

export interface CourseData {
  courseName: string;
  collegeName: string;
  defermentStartDate?: Date;
  actualEndDate?: Date;
  resumptionDate?: Date;
  adjustedResumptionDate?: Date;
  defermentDuration?: number; // in weeks
  newCourseEndDate?: Date;
  originalCourseDuration?: number; // in weeks
  startDate?: Date; // for subsequent courses
}

// Calculation results
export interface DefermentCalculation {
  defermentDuration: number;
  adjustedResumptionDate: Date;
  newCourseEndDate: Date;
}
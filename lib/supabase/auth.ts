import { createClient } from '@supabase/supabase-js';

// Initialize the Supabase client with environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://djyzepqwkquomocdgnpd.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqeXplcHF3a3F1b21vY2RnbnBkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAzMDE5NzMsImV4cCI6MjA2NTg3Nzk3M30.uvxsYk9vFB5eiRpnPBCrj63YmQfK9SZD_CpRvvlmxcs';

// Create a single supabase client for interacting with your database
export const supabaseAuth = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    storageKey: 'supabase-auth',
    autoRefreshToken: true,
    detectSessionInUrl: true,
  }
});

// Types for authentication
export interface AuthUser {
  id: string;
  email: string;
  role?: string;
  user_metadata?: {
    full_name?: string;
  };
}

// Auth helper functions
export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabaseAuth.auth.signInWithPassword({
    email,
    password,
  });

  return { data, error };
};

export const signOut = async () => {
  const { error } = await supabaseAuth.auth.signOut();
  return { error };
};

export const getCurrentUser = async () => {
  const { data: { session }, error } = await supabaseAuth.auth.getSession();

  if (error || !session) {
    return { user: null, error };
  }

  return { user: session.user, error: null };
};

export const resetPassword = async (email: string) => {
  const { data, error } = await supabaseAuth.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/reset-password`,
  });

  return { data, error };
};

export const updatePassword = async (password: string) => {
  const { data, error } = await supabaseAuth.auth.updateUser({
    password,
  });

  return { data, error };
};

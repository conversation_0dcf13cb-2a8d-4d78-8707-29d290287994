import { supabase, Provider, Faculty, Course, Location, Intake, LegacyCollege, LegacyCourse } from './client';

export class SupabaseService {
  // Provider operations
  async getAllProviders(): Promise<Provider[]> {
    const { data, error } = await supabase
      .from('providers')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching providers:', error);
      throw new Error(`Failed to fetch providers: ${error.message}`);
    }

    return data || [];
  }

  // Faculty operations
  async getFacultiesByProvider(providerId: string): Promise<Faculty[]> {
    const { data, error } = await supabase
      .from('faculties')
      .select('*')
      .eq('provider_id', providerId)
      .order('name');

    if (error) {
      console.error('Error fetching faculties:', error);
      throw new Error(`Failed to fetch faculties: ${error.message}`);
    }

    return data || [];
  }

  async getAllFaculties(): Promise<Faculty[]> {
    const { data, error } = await supabase
      .from('faculties')
      .select(`
        *,
        provider:providers(*)
      `)
      .order('name');

    if (error) {
      console.error('Error fetching faculties:', error);
      throw new Error(`Failed to fetch faculties: ${error.message}`);
    }

    return data || [];
  }

  // Course operations
  async getAllCourses(): Promise<Course[]> {
    const { data, error } = await supabase
      .from('courses')
      .select(`
        *,
        faculty:faculties(
          *,
          provider:providers(*)
        )
      `)
      .order('course_name');

    if (error) {
      console.error('Error fetching courses:', error);
      throw new Error(`Failed to fetch courses: ${error.message}`);
    }

    return data || [];
  }

  async getCoursesByProvider(providerId: string): Promise<Course[]> {
    const { data, error } = await supabase
      .from('courses')
      .select(`
        *,
        faculty:faculties!inner(
          *,
          provider:providers!inner(*)
        )
      `)
      .eq('faculty.provider.id', providerId)
      .order('course_name');

    if (error) {
      console.error('Error fetching courses by provider:', error);
      throw new Error(`Failed to fetch courses: ${error.message}`);
    }

    return data || [];
  }

  async getCoursesByFaculty(facultyId: string): Promise<Course[]> {
    const { data, error } = await supabase
      .from('courses')
      .select(`
        *,
        faculty:faculties(
          *,
          provider:providers(*)
        )
      `)
      .eq('faculty_id', facultyId)
      .order('course_name');

    if (error) {
      console.error('Error fetching courses by faculty:', error);
      throw new Error(`Failed to fetch courses: ${error.message}`);
    }

    return data || [];
  }

  // Location operations
  async getAllLocations(): Promise<Location[]> {
    const { data, error } = await supabase
      .from('locations')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching locations:', error);
      throw new Error(`Failed to fetch locations: ${error.message}`);
    }

    return data || [];
  }

  // Intake operations
  async getIntakesByCourse(courseId: string): Promise<Intake[]> {
    const { data, error } = await supabase
      .from('intakes')
      .select(`
        *,
        location:locations(*)
      `)
      .eq('course_id', courseId)
      .order('intake_date');

    if (error) {
      console.error('Error fetching intakes:', error);
      throw new Error(`Failed to fetch intakes: ${error.message}`);
    }

    return data || [];
  }

  async getIntakesByLocation(locationId: string): Promise<Intake[]> {
    const { data, error } = await supabase
      .from('intakes')
      .select(`
        *,
        course:courses(
          *,
          faculty:faculties(
            *,
            provider:providers(*)
          )
        )
      `)
      .eq('location_id', locationId)
      .order('intake_date');

    if (error) {
      console.error('Error fetching intakes by location:', error);
      throw new Error(`Failed to fetch intakes: ${error.message}`);
    }

    return data || [];
  }

  async getIntakesByDateRange(startDate: string, endDate: string): Promise<Intake[]> {
    const { data, error } = await supabase
      .from('intakes')
      .select(`
        *,
        course:courses(
          *,
          faculty:faculties(
            *,
            provider:providers(*)
          )
        ),
        location:locations(*)
      `)
      .gte('intake_date', startDate)
      .lte('intake_date', endDate)
      .order('intake_date');

    if (error) {
      console.error('Error fetching intakes by date range:', error);
      throw new Error(`Failed to fetch intakes: ${error.message}`);
    }

    return data || [];
  }

  async getNextAvailableIntakes(courseId: string, afterDate: string): Promise<Intake[]> {
    const { data, error } = await supabase
      .from('intakes')
      .select(`
        *,
        location:locations(*)
      `)
      .eq('course_id', courseId)
      .gte('intake_date', afterDate)
      .order('intake_date')
      .limit(10);

    if (error) {
      console.error('Error fetching next available intakes:', error);
      throw new Error(`Failed to fetch next available intakes: ${error.message}`);
    }

    return data || [];
  }

    // Complex queries for course-location relationships
  async getCoursesWithLocations(): Promise<Course[]> {
    // Get all unique course-location combinations from intakes
    const { data, error } = await supabase
      .from('intakes')
      .select(`
        course:courses(
          id,
          course_name,
          vet_code,
          cricos_code,
          faculty:faculties(
            name,
            provider:providers(name)
          )
        ),
        location:locations(*)
      `);

    if (error) {
      console.error('Error fetching courses with locations:', error);
      throw new Error(`Failed to fetch courses with locations: ${error.message}`);
    }

    // Group by course and aggregate locations
    const courseMap = new Map<string, Course & { locations: Location[] }>();

    data?.forEach((item: any) => {
      if (!item.course || !item.location) return;

      const courseId = item.course.id;
      if (!courseMap.has(courseId)) {
        courseMap.set(courseId, {
          ...item.course,
          locations: []
        });
      }

      const course = courseMap.get(courseId)!;
      if (!course.locations.some(loc => loc.id === item.location.id)) {
        course.locations.push(item.location);
      }
    });

    // Sort courses by name and return
    return Array.from(courseMap.values()).sort((a, b) =>
      a.course_name.localeCompare(b.course_name)
    );
  }

  async getCourseLocationsByProvider(providerId: string): Promise<Course[]> {
    const { data, error } = await supabase
      .from('intakes')
      .select(`
        course:courses!inner(
          id,
          course_name,
          vet_code,
          cricos_code,
          faculty:faculties!inner(
            name,
            provider:providers!inner(
              id,
              name
            )
          )
        ),
        location:locations(*)
      `)
      .eq('course.faculty.provider.id', providerId);

    if (error) {
      console.error('Error fetching course locations by provider:', error);
      throw new Error(`Failed to fetch course locations: ${error.message}`);
    }

        // Group by course and aggregate locations
    const courseMap = new Map<string, Course & { locations: Location[] }>();

    data?.forEach((item: any) => {
      if (!item.course || !item.location) return;

      const courseId = item.course.id;
      if (!courseMap.has(courseId)) {
        courseMap.set(courseId, {
          ...item.course,
          locations: []
        });
      }

      const course = courseMap.get(courseId)!;
      if (!course.locations.some(loc => loc.id === item.location.id)) {
        course.locations.push(item.location);
      }
    });

    // Sort courses by name and return
    return Array.from(courseMap.values()).sort((a, b) =>
      a.course_name.localeCompare(b.course_name)
    );
  }

  // Legacy compatibility methods for existing components
  async getLegacyFormat(): Promise<LegacyCollege[]> {
    try {
      const providers = await this.getAllProviders();
      const legacyColleges: LegacyCollege[] = [];

      for (const provider of providers) {
        const courses = await this.getCourseLocationsByProvider(provider.id);
        const legacyCourses: LegacyCourse[] = [];

        for (const course of courses) {
          const intakes = await this.getIntakesByCourse(course.id);

          // If no intakes found, generate some sample dates for demo purposes
          const intakeData = intakes.length > 0 ? intakes : this.generateSampleIntakes();

          // Format course name with locations (maintaining compatibility)
          const locationCodes = course.locations?.map(loc => loc.code || loc.name).join(', ') || '';
          const formattedCourseName = locationCodes
            ? `${course.course_name} (${locationCodes})`
            : course.course_name;

          legacyCourses.push({
            courseName: formattedCourseName,
            intakes: intakeData.map(intake => ({
              date: typeof intake === 'string' ? intake : intake.intake_date
            }))
          });
        }

        if (legacyCourses.length > 0) {
          legacyColleges.push({
            collegeName: provider.name,
            courses: legacyCourses
          });
        }
      }

      return legacyColleges;
    } catch (error) {
      console.error('Error converting to legacy format:', error);
      throw error;
    }
  }

  // Generate sample intake dates for demo purposes when database is empty
  private generateSampleIntakes(): string[] {
    const sampleDates: string[] = [];
    const today = new Date();

    // Generate 6 intake dates over the next 12 months
    for (let i = 1; i <= 6; i++) {
      const futureDate = new Date(today);
      futureDate.setMonth(today.getMonth() + (i * 2)); // Every 2 months

      // Adjust to start of Monday
      const dayOfWeek = futureDate.getDay();
      const daysToMonday = dayOfWeek === 0 ? 1 : (8 - dayOfWeek) % 7;
      futureDate.setDate(futureDate.getDate() + daysToMonday);

      sampleDates.push(futureDate.toISOString().split('T')[0]);
    }

    return sampleDates;
  }

  // Search functionality
  async searchCourses(searchTerm: string): Promise<Course[]> {
    const { data, error } = await supabase
      .from('courses')
      .select(`
        *,
        faculty:faculties(
          *,
          provider:providers(*)
        )
      `)
      .or(`course_name.ilike.%${searchTerm}%,vet_code.ilike.%${searchTerm}%,cricos_code.ilike.%${searchTerm}%`)
      .order('course_name');

    if (error) {
      console.error('Error searching courses:', error);
      throw new Error(`Failed to search courses: ${error.message}`);
    }

    return data || [];
  }

  // Statistics
  async getStats(): Promise<{
    providers: number;
    faculties: number;
    courses: number;
    locations: number;
    intakes: number;
  }> {
    try {
      const [providersRes, facultiesRes, coursesRes, locationsRes, intakesRes] = await Promise.all([
        supabase.from('providers').select('id', { count: 'exact', head: true }),
        supabase.from('faculties').select('id', { count: 'exact', head: true }),
        supabase.from('courses').select('id', { count: 'exact', head: true }),
        supabase.from('locations').select('id', { count: 'exact', head: true }),
        supabase.from('intakes').select('id', { count: 'exact', head: true })
      ]);

      return {
        providers: providersRes.count || 0,
        faculties: facultiesRes.count || 0,
        courses: coursesRes.count || 0,
        locations: locationsRes.count || 0,
        intakes: intakesRes.count || 0
      };
    } catch (error) {
      console.error('Error fetching stats:', error);
      return { providers: 0, faculties: 0, courses: 0, locations: 0, intakes: 0 };
    }
  }
}

// Export singleton instance
export const supabaseService = new SupabaseService();

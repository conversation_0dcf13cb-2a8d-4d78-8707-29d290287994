import { supabase } from './client';
import type { Course } from '../../components/DefermentCalculatorEnhanced';
import type { FeeAllocationResult } from '../utils/feeAllocationUtils';

// Database types matching our schema
export interface DefermentCalculation {
  id: string;
  title: string;
  user_id?: string;
  created_at: string;
  updated_at: string;
  include_payment_calculation: boolean;
  notes?: string;
  total_courses: number;
  total_deferment_duration_weeks?: number;
}

export interface DefermentCourse {
  id: string;
  calculation_id: string;
  course_order: number;
  created_at: string;
  course_name: string;
  original_start_date?: string;
  original_end_date?: string;
  deferment_start_date?: string;
  resumption_date?: string;
  new_start_date?: string;
  duration_weeks?: number;
  duration_days?: number;
  duration_months?: number;
  duration_type?: 'weeks' | 'days' | 'months';
  duration?: number;
  deferment_duration?: number;
  new_calculated_start_date?: string;
  new_end_date?: string;
  total_material_fee?: number;
  total_placement_fee?: number;
  total_tuition_fee?: number;
  prepayment_total?: number;
  total_all_fees?: number;
  prepaid_amount?: number;
  prepaid_tuition_fee?: number;
  prepaid_material_fee?: number;
  prepaid_placement_fee?: number;
  other_pre_paid_non_tuition_fee?: number;
  coverage_start_date?: string;
  coverage_end_date?: string;
  student_id?: string;
  student_name?: string;
  course_code?: string;
  tuition_fees_paid_so_far?: number;
  materials_fees_paid?: number;
  course_commencement_date?: string;
}

export interface DefermentFeeAllocation {
  id: string;
  course_id: string;
  created_at: string;
  unspent_tuition_fees?: number;
  is_student_in_credit?: boolean;
  amount_owed?: number;
  new_total_tuition_fee?: number;
  initial_deposit?: number;
  course_completion_percentage?: number;
  fees_earned_by_institution?: number;
  can_proceed_with_deferment?: boolean;
  status_message?: string;
  email_data?: any;
}

export interface SaveCalculationRequest {
  title: string;
  courses: Course[];
  includePaymentCalculation: boolean;
  notes?: string;
}

export interface CalculationWithCourses extends DefermentCalculation {
  courses: DefermentCourse[];
}

/**
 * Save a complete deferment calculation to the database
 */
export async function saveDefermentCalculation(request: SaveCalculationRequest): Promise<{ success: boolean; calculationId?: string; error?: string }> {
  try {
    // Calculate total deferment duration
    const totalDefermentDuration = request.courses.reduce((sum, course) => sum + (course.defermentDuration || 0), 0);
    
    // Insert the main calculation record
    const { data: calculation, error: calcError } = await supabase
      .from('deferment_calculations')
      .insert({
        title: request.title,
        include_payment_calculation: request.includePaymentCalculation,
        notes: request.notes,
        total_courses: request.courses.length,
        total_deferment_duration_weeks: totalDefermentDuration
      })
      .select()
      .single();

    if (calcError) {
      console.error('Error saving calculation:', calcError);
      return { success: false, error: calcError.message };
    }

    // Insert courses
    const coursesToInsert = request.courses.map((course, index) => ({
      calculation_id: calculation.id,
      course_order: index + 1,
      course_name: course.courseName,
      original_start_date: course.originalStartDate || null,
      original_end_date: course.originalEndDate || null,
      deferment_start_date: course.defermentStartDate || null,
      resumption_date: course.resumptionDate || null,
      new_start_date: course.newStartDate || null,
      duration_weeks: course.durationWeeks,
      duration_days: course.durationDays,
      duration_months: course.durationMonths,
      duration_type: course.durationType,
      duration: course.duration,
      deferment_duration: course.defermentDuration,
      new_calculated_start_date: course.newCalculatedStartDate,
      new_end_date: course.newEndDate,
      total_material_fee: course.totalMaterialFee,
      total_placement_fee: course.totalPlacementFee,
      total_tuition_fee: course.totalTuitionFee,
      prepayment_total: course.prepaymentTotal,
      total_all_fees: course.totalAllFees,
      prepaid_amount: course.prepaidAmount,
      prepaid_tuition_fee: course.prepaidTuitionFee,
      prepaid_material_fee: course.prepaidMaterialFee,
      prepaid_placement_fee: course.prepaidPlacementFee,
      other_pre_paid_non_tuition_fee: course.otherPrePaidNonTuitionFee,
      coverage_start_date: course.coverageStartDate,
      coverage_end_date: course.coverageEndDate,
      student_id: course.studentId,
      student_name: course.studentName,
      course_code: course.courseCode,
      tuition_fees_paid_so_far: course.tuitionFeesPaidSoFar,
      materials_fees_paid: course.materialsFeesPaid,
      course_commencement_date: course.courseCommencementDate
    }));

    const { data: courses, error: coursesError } = await supabase
      .from('deferment_courses')
      .insert(coursesToInsert)
      .select();

    if (coursesError) {
      console.error('Error saving courses:', coursesError);
      return { success: false, error: coursesError.message };
    }

    // Insert fee allocation results if they exist
    for (let i = 0; i < request.courses.length; i++) {
      const course = request.courses[i];
      const savedCourse = courses[i];
      
      if (course.feeAllocationResult && savedCourse) {
        const feeAllocation = course.feeAllocationResult;
        
        const { error: feeError } = await supabase
          .from('deferment_fee_allocations')
          .insert({
            course_id: savedCourse.id,
            unspent_tuition_fees: feeAllocation.unspentTuitionFees,
            is_student_in_credit: feeAllocation.isStudentInCredit,
            amount_owed: feeAllocation.amountOwed,
            new_total_tuition_fee: feeAllocation.newTotalTuitionFee,
            initial_deposit: feeAllocation.initialDeposit,
            course_completion_percentage: feeAllocation.courseCompletionPercentage,
            fees_earned_by_institution: feeAllocation.feesEarnedByInstitution,
            can_proceed_with_deferment: feeAllocation.canProceedWithDeferment,
            status_message: feeAllocation.statusMessage,
            email_data: feeAllocation.emailData
          });

        if (feeError) {
          console.error('Error saving fee allocation:', feeError);
          // Continue with other fee allocations even if one fails
        }
      }
    }

    return { success: true, calculationId: calculation.id };
  } catch (error) {
    console.error('Unexpected error saving calculation:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get all saved deferment calculations
 */
export async function getDefermentCalculations(): Promise<{ success: boolean; calculations?: CalculationWithCourses[]; error?: string }> {
  try {
    const { data: calculations, error } = await supabase
      .from('deferment_calculations')
      .select(`
        *,
        courses:deferment_courses(
          *,
          fee_allocation:deferment_fee_allocations(*)
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching calculations:', error);
      return { success: false, error: error.message };
    }

    return { success: true, calculations: calculations || [] };
  } catch (error) {
    console.error('Unexpected error fetching calculations:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get a specific deferment calculation by ID
 */
export async function getDefermentCalculation(id: string): Promise<{ success: boolean; calculation?: CalculationWithCourses; error?: string }> {
  try {
    const { data: calculation, error } = await supabase
      .from('deferment_calculations')
      .select(`
        *,
        courses:deferment_courses(
          *,
          fee_allocation:deferment_fee_allocations(*)
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching calculation:', error);
      return { success: false, error: error.message };
    }

    return { success: true, calculation };
  } catch (error) {
    console.error('Unexpected error fetching calculation:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Delete a deferment calculation
 */
export async function deleteDefermentCalculation(id: string): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('deferment_calculations')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting calculation:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Unexpected error deleting calculation:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Update a deferment calculation title and notes
 */
export async function updateDefermentCalculation(id: string, updates: { title?: string; notes?: string }): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('deferment_calculations')
      .update(updates)
      .eq('id', id);

    if (error) {
      console.error('Error updating calculation:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Unexpected error updating calculation:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Convert database course to component course format
 */
export function convertDatabaseCourseToComponent(dbCourse: DefermentCourse, feeAllocation?: DefermentFeeAllocation): Course {
  return {
    id: parseInt(dbCourse.course_order.toString()),
    courseName: dbCourse.course_name,
    originalStartDate: dbCourse.original_start_date || '',
    originalEndDate: dbCourse.original_end_date || '',
    defermentStartDate: dbCourse.deferment_start_date || '',
    resumptionDate: dbCourse.resumption_date || '',
    newStartDate: dbCourse.new_start_date || '',
    durationWeeks: dbCourse.duration_weeks || 0,
    durationDays: dbCourse.duration_days || 0,
    durationMonths: dbCourse.duration_months || 0,
    durationType: dbCourse.duration_type || 'weeks',
    duration: dbCourse.duration || 0,
    defermentDuration: dbCourse.deferment_duration || 0,
    newCalculatedStartDate: dbCourse.new_calculated_start_date || '',
    newEndDate: dbCourse.new_end_date || '',
    totalMaterialFee: dbCourse.total_material_fee,
    totalPlacementFee: dbCourse.total_placement_fee,
    totalTuitionFee: dbCourse.total_tuition_fee,
    prepaymentTotal: dbCourse.prepayment_total,
    totalAllFees: dbCourse.total_all_fees,
    prepaidAmount: dbCourse.prepaid_amount,
    prepaidTuitionFee: dbCourse.prepaid_tuition_fee,
    prepaidMaterialFee: dbCourse.prepaid_material_fee,
    prepaidPlacementFee: dbCourse.prepaid_placement_fee,
    otherPrePaidNonTuitionFee: dbCourse.other_pre_paid_non_tuition_fee,
    coverageStartDate: dbCourse.coverage_start_date,
    coverageEndDate: dbCourse.coverage_end_date,
    studentId: dbCourse.student_id,
    studentName: dbCourse.student_name,
    courseCode: dbCourse.course_code,
    tuitionFeesPaidSoFar: dbCourse.tuition_fees_paid_so_far,
    materialsFeesPaid: dbCourse.materials_fees_paid,
    courseCommencementDate: dbCourse.course_commencement_date,
    feeAllocationResult: feeAllocation ? {
      unspentTuitionFees: feeAllocation.unspent_tuition_fees || 0,
      isStudentInCredit: feeAllocation.is_student_in_credit || false,
      amountOwed: feeAllocation.amount_owed || 0,
      newTotalTuitionFee: feeAllocation.new_total_tuition_fee || 0,
      initialDeposit: feeAllocation.initial_deposit || 0,
      courseCompletionPercentage: feeAllocation.course_completion_percentage || 0,
      feesEarnedByInstitution: feeAllocation.fees_earned_by_institution || 0,
      canProceedWithDeferment: feeAllocation.can_proceed_with_deferment || false,
      statusMessage: feeAllocation.status_message || '',
      emailData: feeAllocation.email_data || {}
    } as FeeAllocationResult : undefined
  };
}

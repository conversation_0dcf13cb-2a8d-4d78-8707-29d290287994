import { supabase } from './client';
import type { CTCalculationResult } from '../ai/ct-calculator';

// Database types for CT calculations
export interface CTCalculationRecord {
  id: string;
  title: string;
  user_id?: string;
  created_at: string;
  updated_at: string;
  workflow_type: string;
  student_name?: string;
  course_name?: string;
  original_fee: number;
  new_fee: number;
  savings: number;
  formula: string;
  units_reduction: number;
  notes?: string;
  calculation_data: any; // JSON field for storing full calculation details
}

export interface SaveCTCalculationRequest {
  title: string;
  calculation: CTCalculationResult;
  notes?: string;
}

/**
 * Save a CT calculation to the database
 */
export async function saveCTCalculation(request: SaveCTCalculationRequest): Promise<{ success: boolean; calculationId?: string; error?: string }> {
  try {
    const { calculation } = request;
    
    // Prepare calculation data for database
    const calculationData = {
      title: request.title,
      workflow_type: calculation.workflowType,
      student_name: calculation.studentName || null,
      course_name: calculation.courseName || null,
      original_fee: calculation.originalFee,
      new_fee: calculation.newFee,
      savings: calculation.savings,
      formula: calculation.formula,
      units_reduction: calculation.unitsReduction,
      notes: request.notes || null,
      calculation_data: calculation, // Store full calculation as JSON
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Insert the calculation record
    const { data: savedCalculation, error: saveError } = await supabase
      .from('ct_calculations')
      .insert(calculationData)
      .select()
      .single();

    if (saveError) {
      console.error('Error saving CT calculation:', saveError);
      return { success: false, error: saveError.message };
    }

    return { 
      success: true, 
      calculationId: savedCalculation.id 
    };
  } catch (error) {
    console.error('Unexpected error saving CT calculation:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

/**
 * Get all CT calculations for the current user
 */
export async function getCTCalculations(): Promise<{ success: boolean; calculations?: CTCalculationRecord[]; error?: string }> {
  try {
    const { data, error } = await supabase
      .from('ct_calculations')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching CT calculations:', error);
      return { success: false, error: error.message };
    }

    return { success: true, calculations: data || [] };
  } catch (error) {
    console.error('Unexpected error fetching CT calculations:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

/**
 * Get a specific CT calculation by ID
 */
export async function getCTCalculationById(id: string): Promise<{ success: boolean; calculation?: CTCalculationRecord; error?: string }> {
  try {
    const { data, error } = await supabase
      .from('ct_calculations')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching CT calculation:', error);
      return { success: false, error: error.message };
    }

    return { success: true, calculation: data };
  } catch (error) {
    console.error('Unexpected error fetching CT calculation:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

/**
 * Delete a CT calculation by ID
 */
export async function deleteCTCalculation(id: string): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('ct_calculations')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting CT calculation:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Unexpected error deleting CT calculation:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

/**
 * Update a CT calculation
 */
export async function updateCTCalculation(id: string, updates: Partial<SaveCTCalculationRequest>): Promise<{ success: boolean; error?: string }> {
  try {
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (updates.title) updateData.title = updates.title;
    if (updates.notes !== undefined) updateData.notes = updates.notes;
    if (updates.calculation) {
      updateData.calculation_data = updates.calculation;
      updateData.workflow_type = updates.calculation.workflowType;
      updateData.student_name = updates.calculation.studentName || null;
      updateData.course_name = updates.calculation.courseName || null;
      updateData.original_fee = updates.calculation.originalFee;
      updateData.new_fee = updates.calculation.newFee;
      updateData.savings = updates.calculation.savings;
      updateData.formula = updates.calculation.formula;
      updateData.units_reduction = updates.calculation.unitsReduction;
    }

    const { error } = await supabase
      .from('ct_calculations')
      .update(updateData)
      .eq('id', id);

    if (error) {
      console.error('Error updating CT calculation:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Unexpected error updating CT calculation:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

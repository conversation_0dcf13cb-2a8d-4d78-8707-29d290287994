/**
 * CT Calculator - Core calculation logic for Credit Transfer applications
 * Implements unit-based calculations with three distinct workflows
 */

export interface CTCalculationInput {
  originalFee: number;
  totalUnits: number;
  unitsAfterCT: number;
  invoiceAmount?: number; // Only for First Aid workflow
  workflowType: 'first_aid' | 'general_ct' | 'coq_packaging';
  courseName?: string;
  studentName?: string;
}

export interface CTCalculationResult {
  workflowType: string;
  originalFee: number;
  newFee: number;
  savings: number;
  formula: string;
  unitsReduction: number;
  courseName?: string;
  studentName?: string;
  calculatedAt: Date;
}

export interface UnitTableEntry {
  unitCode: string;
  unitName: string;
  creditStatus: 'granted' | 'not_granted' | 'pending';
  creditPoints?: number;
}

export interface ParsedUnitTable {
  totalUnits: number;
  unitsAfterCT: number;
  grantedUnits: UnitTableEntry[];
  notGrantedUnits: UnitTableEntry[];
  summary: string;
}

/**
 * Main calculation function for CT fee adjustments
 */
export function calculateCTFee(input: CTCalculationInput): CTCalculationResult {
  const { originalFee, totalUnits, unitsAfterCT, invoiceAmount, workflowType, courseName, studentName } = input;

  // Validate inputs
  if (originalFee < 0) {
    throw new Error('Original fee cannot be negative');
  }
  if (totalUnits <= 0) {
    throw new Error('Total units must be greater than 0');
  }
  if (unitsAfterCT < 0) {
    throw new Error('Units after CT cannot be negative');
  }
  if (unitsAfterCT > totalUnits) {
    throw new Error('Units after CT cannot exceed total units');
  }

  switch (workflowType) {
    case 'first_aid':
      return calculateFirstAidCT(input);
    
    case 'general_ct':
      return calculateGeneralCT(input);
    
    case 'coq_packaging':
      return calculateCOQPackaging(input);
    
    default:
      throw new Error('Invalid workflow type');
  }
}

/**
 * First Aid CT Workflow - Duration never changes, fee reduction based on invoice
 */
function calculateFirstAidCT(input: CTCalculationInput): CTCalculationResult {
  const { originalFee, invoiceAmount, courseName, studentName } = input;
  
  const firstAidReduction = invoiceAmount || 0;
  const newFee = Math.max(0, originalFee - firstAidReduction);
  
  return {
    workflowType: 'First Aid CT',
    originalFee,
    newFee,
    savings: firstAidReduction,
    formula: invoiceAmount 
      ? `Fee reduction = Invoice amount ($${invoiceAmount.toLocaleString()})` 
      : 'No invoice provided = No fee reduction',
    unitsReduction: 0, // Duration never changes for First Aid
    courseName,
    studentName,
    calculatedAt: new Date()
  };
}

/**
 * General CT Workflow - Standard unit-based calculation
 */
function calculateGeneralCT(input: CTCalculationInput): CTCalculationResult {
  const { originalFee, totalUnits, unitsAfterCT, courseName, studentName } = input;
  
  const newFee = (originalFee / totalUnits) * unitsAfterCT;
  const savings = originalFee - newFee;
  
  return {
    workflowType: 'General CT',
    originalFee,
    newFee: Math.round(newFee * 100) / 100, // Round to 2 decimal places
    savings: Math.round(savings * 100) / 100,
    formula: `New Fee = ($${originalFee.toLocaleString()} ÷ ${totalUnits}) × ${unitsAfterCT} = $${newFee.toLocaleString()}`,
    unitsReduction: totalUnits - unitsAfterCT,
    courseName,
    studentName,
    calculatedAt: new Date()
  };
}

/**
 * Change of Qualification (COQ) Workflow - Same formula applied to restructured plan
 */
function calculateCOQPackaging(input: CTCalculationInput): CTCalculationResult {
  const { originalFee, totalUnits, unitsAfterCT, courseName, studentName } = input;
  
  const newFee = (originalFee / totalUnits) * unitsAfterCT;
  const savings = originalFee - newFee;
  
  return {
    workflowType: 'Change of Qualification',
    originalFee,
    newFee: Math.round(newFee * 100) / 100,
    savings: Math.round(savings * 100) / 100,
    formula: `New Fee = ($${originalFee.toLocaleString()} ÷ ${totalUnits}) × ${unitsAfterCT} = $${newFee.toLocaleString()}`,
    unitsReduction: totalUnits - unitsAfterCT,
    courseName,
    studentName,
    calculatedAt: new Date()
  };
}

/**
 * Parse unit table from text input (copied from emails/documents)
 */
export function parseUnitTable(tableText: string): ParsedUnitTable {
  const lines = tableText.split('\n').filter(line => line.trim());
  const grantedUnits: UnitTableEntry[] = [];
  const notGrantedUnits: UnitTableEntry[] = [];
  
  for (const line of lines) {
    // Skip header lines
    if (line.toLowerCase().includes('unit code') || line.toLowerCase().includes('unit name')) {
      continue;
    }
    
    // Try to extract unit information
    const unitMatch = line.match(/([A-Z]{3,}\d{3,})\s+(.+?)\s+(granted|not granted|pending)/i);
    if (unitMatch) {
      const [, unitCode, unitName, status] = unitMatch;
      const entry: UnitTableEntry = {
        unitCode: unitCode.trim(),
        unitName: unitName.trim(),
        creditStatus: status.toLowerCase().replace(' ', '_') as 'granted' | 'not_granted' | 'pending'
      };
      
      if (entry.creditStatus === 'granted') {
        grantedUnits.push(entry);
      } else {
        notGrantedUnits.push(entry);
      }
    }
  }
  
  const totalUnits = grantedUnits.length + notGrantedUnits.length;
  const unitsAfterCT = notGrantedUnits.length; // Units remaining after CT
  
  return {
    totalUnits,
    unitsAfterCT,
    grantedUnits,
    notGrantedUnits,
    summary: `Total: ${totalUnits} units, Credited: ${grantedUnits.length} units, Remaining: ${unitsAfterCT} units`
  };
}

/**
 * Detect workflow type from input text
 */
export function detectWorkflowType(inputText: string): 'first_aid' | 'general_ct' | 'coq_packaging' {
  const text = inputText.toLowerCase();
  
  // First Aid indicators
  if (text.includes('hltaid') || text.includes('first aid') || text.includes('cpr')) {
    return 'first_aid';
  }
  
  // COQ indicators
  if (text.includes('change of qualification') || text.includes('coq') || text.includes('new course')) {
    return 'coq_packaging';
  }
  
  // Default to general CT
  return 'general_ct';
}

/**
 * Generate compliance checklist based on workflow type
 */
export function generateComplianceChecklist(workflowType: string): string[] {
  const baseChecklist = [
    'Student written agreement received',
    'Supporting documentation verified',
    'FLOO promotional restrictions checked'
  ];
  
  switch (workflowType) {
    case 'First Aid CT':
      return [
        ...baseChecklist,
        'First Aid certificate verified',
        'Invoice amount confirmed',
        'Duration remains unchanged'
      ];
    
    case 'General CT':
      return [
        ...baseChecklist,
        'CT Form completed',
        'Certificate/Statement of Attainment verified',
        'Unit mapping confirmed',
        'Course end date updated'
      ];
    
    case 'Change of Qualification':
      return [
        ...baseChecklist,
        'COQ Application submitted',
        'Academic Progression Report reviewed',
        'New study plan approved',
        'All course fees recalculated'
      ];
    
    default:
      return baseChecklist;
  }
}

/**
 * Generate Portal and PRISMS instructions
 */
export function generateInstructions(result: CTCalculationResult): {
  portalInstructions: string[];
  prismsInstructions: string[];
} {
  const portalInstructions = [
    `Update 'Total Tuition Fee' to $${result.newFee.toLocaleString()}`,
    'Add CT application note to student record',
    'Update course end date if applicable'
  ];
  
  const prismsInstructions = [
    'Cancel existing CoE',
    `Issue new CoE with updated fee: $${result.newFee.toLocaleString()}`,
    'Update course duration if changed',
    'Submit variation to PRISMS'
  ];
  
  if (result.workflowType === 'First Aid CT') {
    portalInstructions.push('Note: Course duration unchanged for First Aid CT');
    prismsInstructions.push('Note: No duration change required');
  }
  
  return { portalInstructions, prismsInstructions };
}

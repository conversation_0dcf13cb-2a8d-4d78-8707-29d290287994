/**
 * Universal AI Provider - Multi-provider support for CT Calculator
 * Supports OpenAI, Groq, Together AI, Anthropic, Moonshot AI, and other OpenAI-compatible APIs
 */

export interface AIMessage {
  role: "system" | "user" | "assistant";
  content: string;
}

export interface AIResponse {
  content: string;
  usage?: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
  };
  model?: string;
  provider?: string;
}

export interface AIProviderConfig {
  provider: string;
  baseURL: string;
  apiKey: string;
  modelName: string;
  temperature?: number;
  maxTokens?: number;
}

export class UniversalAIProvider {
  private config: AIProviderConfig;

  constructor(config?: Partial<AIProviderConfig>) {
    this.config = {
      provider: config?.provider || process.env.AI_PROVIDER || "groq",
      baseURL:
        config?.baseURL ||
        process.env.AI_BASE_URL ||
        "https://api.groq.com/openai/v1",
      apiKey: config?.apiKey || process.env.AI_API_KEY || "",
      modelName:
        config?.modelName || process.env.AI_MODEL_NAME || "llama3-70b-8192",
      temperature: config?.temperature || 0.1,
      maxTokens: config?.maxTokens || 2000,
    };

    if (!this.config.apiKey) {
      throw new Error("AI API key is required");
    }
  }

  /**
   * Send messages to AI provider
   */
  async sendMessage(
    messages: AIMessage[],
    options: Partial<AIProviderConfig> = {}
  ): Promise<AIResponse> {
    const requestConfig = { ...this.config, ...options };

    try {
      const response = await this.makeRequest(messages, requestConfig);
      return this.parseResponse(response, requestConfig.provider);
    } catch (error) {
      console.error("AI Provider Error:", error);
      throw new Error(
        `Failed to get response from ${requestConfig.provider}: ${error}`
      );
    }
  }

  /**
   * Make HTTP request to AI provider
   */
  private async makeRequest(
    messages: AIMessage[],
    config: AIProviderConfig
  ): Promise<any> {
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Set authorization header based on provider
    if (config.provider === "anthropic") {
      headers["x-api-key"] = config.apiKey;
      headers["anthropic-version"] = "2023-06-01";
    } else {
      headers["Authorization"] = `Bearer ${config.apiKey}`;
    }

    const body = this.buildRequestBody(messages, config);

    const response = await fetch(`${config.baseURL}/chat/completions`, {
      method: "POST",
      headers,
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    return response.json();
  }

  /**
   * Build request body based on provider
   */
  private buildRequestBody(
    messages: AIMessage[],
    config: AIProviderConfig
  ): any {
    const baseBody = {
      model: config.modelName,
      messages: messages,
      temperature: config.temperature,
      max_tokens: config.maxTokens,
    };

    // Provider-specific adjustments
    switch (config.provider) {
      case "anthropic":
        return {
          ...baseBody,
          max_tokens: config.maxTokens, // Anthropic uses max_tokens
        };

      case "groq":
        return {
          ...baseBody,
          stream: false, // Ensure no streaming for Groq
        };

      case "together":
        return {
          ...baseBody,
          stop: ["<|eot_id|>"], // Together AI stop tokens
        };

      case "moonshot":
        return {
          ...baseBody,
          stream: false, // Moonshot AI - disable streaming
        };

      default:
        return baseBody;
    }
  }

  /**
   * Parse response based on provider
   */
  private parseResponse(data: any, provider: string): AIResponse {
    try {
      // Standard OpenAI-compatible response
      if (data.choices && data.choices[0] && data.choices[0].message) {
        return {
          content: data.choices[0].message.content,
          usage: data.usage,
          model: data.model,
          provider,
        };
      }

      // Anthropic response format
      if (data.content && Array.isArray(data.content)) {
        return {
          content: data.content[0].text,
          usage: data.usage,
          model: data.model,
          provider,
        };
      }

      throw new Error("Unexpected response format");
    } catch (error) {
      console.error("Response parsing error:", error);
      throw new Error(`Failed to parse response from ${provider}`);
    }
  }

  /**
   * Get available models for current provider
   */
  async getAvailableModels(): Promise<string[]> {
    const modelMappings: Record<string, string[]> = {
      groq: [
        "llama3-70b-8192",
        "llama3-8b-8192",
        "mixtral-8x7b-32768",
        "gemma-7b-it",
      ],
      openai: [
        "gpt-4-turbo-preview",
        "gpt-4",
        "gpt-3.5-turbo",
        "gpt-3.5-turbo-16k",
      ],
      together: [
        "meta-llama/Llama-2-70b-chat-hf",
        "meta-llama/Llama-2-13b-chat-hf",
        "mistralai/Mixtral-8x7B-Instruct-v0.1",
      ],
      anthropic: [
        "claude-3-sonnet-20240229",
        "claude-3-haiku-20240307",
        "claude-2.1",
      ],
    };

    return modelMappings[this.config.provider] || [];
  }

  /**
   * Test connection to AI provider
   */
  async testConnection(): Promise<boolean> {
    try {
      const testMessages: AIMessage[] = [
        {
          role: "user",
          content: 'Hello, please respond with "OK" to test the connection.',
        },
      ];

      const response = await this.sendMessage(testMessages, { maxTokens: 10 });
      return response.content.toLowerCase().includes("ok");
    } catch (error) {
      console.error("Connection test failed:", error);
      return false;
    }
  }

  /**
   * Get provider configuration
   */
  getConfig(): AIProviderConfig {
    return { ...this.config };
  }

  /**
   * Update provider configuration
   */
  updateConfig(newConfig: Partial<AIProviderConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

/**
 * Factory function to create provider instances
 */
export function createAIProvider(
  config?: Partial<AIProviderConfig>
): UniversalAIProvider {
  return new UniversalAIProvider(config);
}

/**
 * Get default provider configurations
 */
export function getProviderDefaults(): Record<
  string,
  Partial<AIProviderConfig>
> {
  return {
    groq: {
      provider: "groq",
      baseURL: "https://api.groq.com/openai/v1",
      modelName: "llama3-70b-8192",
      temperature: 0.1,
      maxTokens: 2000,
    },
    openai: {
      provider: "openai",
      baseURL: "https://api.openai.com/v1",
      modelName: "gpt-4-turbo-preview",
      temperature: 0.1,
      maxTokens: 2000,
    },
    together: {
      provider: "together",
      baseURL: "https://api.together.xyz/v1",
      modelName: "meta-llama/Llama-2-70b-chat-hf",
      temperature: 0.1,
      maxTokens: 2000,
    },
    anthropic: {
      provider: "anthropic",
      baseURL: "https://api.anthropic.com",
      modelName: "claude-3-sonnet-20240229",
      temperature: 0.1,
      maxTokens: 2000,
    },
  };
}

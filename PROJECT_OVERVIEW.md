# Project Overview: Student Deferment Calculator

This document details the purpose, architecture, and data management strategy of the Student Deferment Calculator application. It also highlights critical areas of concern and provides recommendations to ensure data integrity.

---

### **1. What is this project supposed to do?**

The project has two primary functions:

*   **Student Deferment Calculator (`/deferment-calculator`):** A tool for students and staff to calculate how a deferment period affects a student's study plan. It determines new resumption dates and adjusted course completion dates by consulting a predefined list of course intake dates.
*   **Data Synchronization Admin (`/admin/sync`):** An administrative interface designed to keep the application's database up-to-date. Its sole purpose is to take an external data source—the [`intake-dates.json`](intake-dates.json:1) file—and use it to populate the Supabase database with the correct colleges, courses, and intake schedules.

---

### **2. How is the database linked to the JSON file?**

The link is not continuous; it's a **manual, one-way synchronization process** initiated by an administrator. The [`intake-dates.json`](intake-dates.json:1) file is intended to be the **single source of truth** for course offerings and their specific intake dates.

Here is the intended workflow:

1.  **Prepare Data**: An administrator prepares the master list of courses and their intake dates in the [`intake-dates.json`](intake-dates.json:1) file.
2.  **Upload**: The administrator goes to the [`/admin/sync`](app/admin/sync/page.tsx:22) page, uploads this JSON file. The file is saved to Supabase Storage for auditing and history.
3.  **Initiate Sync**: The administrator clicks the "Sync to Database" button.
4.  **Execute Sync Logic**: This action calls the API endpoint at [`/api/sync-json/route.ts`](app/api/sync-json/route.ts:9). This script is the heart of the operation and is responsible for:
    *   Reading the uploaded JSON file.
    *   Connecting to the Supabase database.
    *   Adding or finding the college (as a `faculty`).
    *   Adding or finding the course under that college.
    *   Deleting all old intake dates for that course to prevent duplicates.
    *   Inserting all the new intake dates from the JSON file.

---

### **3. What went wrong? The source of data corruption.**

The core problem was a dangerous assumption in the sync script. The long, strange CRICOS codes were the main symptom of this issue.

*   **CRICOS Codes Should NEVER Be Generated:** The sync script was programmed to **invent** a CRICOS code if a course didn't already exist in the database. It did this by combining the VET code with random characters (e.g., `CRCHC50121NPA5XV7CF1491`).
*   **Why this is wrong:** CRICOS codes are official, unique, government-issued identifiers. They cannot be guessed or generated. This logic was the direct cause of the `duplicate key` errors and the creation of courses with invalid data. Your database correctly has a `unique constraint` on the `cricos_code` column to prevent this, but the script's attempts to create duplicates caused the sync to fail repeatedly.

---

### **4. Critical things to be careful about for data accuracy.**

Your concern is justified. To protect your data, the following principles **must** be followed.

#### **The Golden Rule: Define Your "Source of Truth"**

The most critical step in ensuring data accuracy is to be disciplined about what each data source is responsible for.

*   **Supabase Database is the "Source of Truth" for *Permanent* Course Data.**
    *   This includes `course_name`, `vet_code`, and especially the official **`cricos_code`**. This data is foundational and should only be changed in the database directly, with great care.
*   **The `intake-dates.json` file is the "Source of Truth" for *Volatile* Schedule Data.**
    *   Its only job is to define **which courses are available at which colleges** and to list their **upcoming intake dates**. It is a schedule, not a course catalog.

#### **The Corrected Workflow**

To prevent any future data corruption, the process must be updated to respect the "source of truth" principle.

1.  **Add/Verify a Course in Supabase First:** Before a new course can be included in a JSON sync, an administrator **must** ensure that the course exists in the `courses` table in Supabase with its **correct, official CRICOS code**.
2.  **Update the JSON Schedule:** The administrator can then add the course and its intake dates to the [`intake-dates.json`](intake-dates.json:1) file. The `courseName` and `collegeName` must be an **exact match** to what is in the database.
3.  **Upload and Sync:** The administrator uses the [`/admin/sync`](app/admin/sync/page.tsx:22) page to upload the file and run the sync.
4.  **Safe Sync Execution:** The updated sync script will now:
    *   Find the existing course in the database using the `course_name` and `collegeName`.
    *   If it finds the course, it will use the existing `course_id` and proceed to update the intake dates.
    *   If it **cannot** find the course, it will **stop and report an error**, preventing the creation of invalid data.

#### **Proposed New Sync Logic:**

The current logic tries to be too "smart" by creating data. A safer approach is to be "dumb" and only update what already exists.

*   For each course in the uploaded JSON file:
    1.  Try to find an **exact match** in the database based on `course_name` and `collegeName` (faculty).
    2.  **If a match is found:** Proceed as planned. Use the existing `course_id`, delete its old intakes, and insert the new ones from the JSON.
    3.  **If NO match is found:** **STOP.** Do not create a new course. Instead, the script should fail and report an error like: `"The course '[Course Name]' for college '[College Name]' was not found in the database. Please add it manually with the correct CRICOS code before syncing."`

This change is critical. It makes the process fail-safe, preventing the script from polluting your database with generated, incorrect data. It forces an administrator to ensure the foundational course data (with its official CRICOS code) is correct in the database *before* trying to update its intake dates.




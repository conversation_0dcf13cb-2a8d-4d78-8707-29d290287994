const fs = require('fs');

// JSON courses (cleaned)
const jsonCourses = new Set([
'bsb40920 certificate iv in project management practice',
'bsb50420 diploma of leadership and management',
'bsb50420 diploma of leadership and management (syd, melb & hob))',
'bsb50820 diploma of project management',
'bsb60120 advanced diploma of business',
'bsb60420 advance diploma of leadership and management',
'bsb60420 advanced diploma of leadership and management',
'bsb60720 advanced diploma of program management',
'bsb80120 graduate diploma of management (learning)',
'bsb80220 graduate diploma of portfolio management',
'bsb80320 graduate diploma of strategic leadership',
'bsb80320 graduate diploma of strategic leadership (melb))',
'chc30121 certificate iii in early childhood education and care',
'chc33021 certificate iii in individual support',
'chc33021 certificate iii in individual support (ageing) 52 weeks',
'chc33021 certificate iii in individual support (disability)',
'chc33021 certificate iii in individual support (disability) (syd, melb, hob)',
'chc33021 certificate iii in individual support - (ageing) (syd, melb, hob)',
'chc43015 certificate iv in ageing support 52 weeks',
'chc43015 certificate iv in ageing support(syd, bsb, hob) 26 weeks',
'chc43121 certificate iv in disability support',
'chc50121 diploma of early childhood education and care',
'chc52021 diploma of community services',
'chc62015 advanced diploma of community sector management',
'fns40222 certificate iv in accounting and bookkeeping',
'fns50222 diploma of accounting',
'fns60222 advanced diploma of accounting',
'fns60222 diploma of accounting'
]);

// Database courses (from the query result)
const dbCourses = [
'advanced diploma of accounting',
'advanced diploma of air conditioning and refrigeration engineering',
'advanced diploma of business',
'advanced diploma of civil construction design',
'advanced diploma of community sector management',
'advanced diploma of electronics and communications engineering',
'advanced diploma of hospitality management',
'advanced diploma of information technology (telecommunications network engineering)',
'advanced diploma of leadership and management',
'advanced diploma of program management',
'advanced diploma of supply chain management',
'bsb40920 certificate iv in project management practice',
'bsb50420 diploma of leadership and management',
'bsb50420 diploma of leadership and management (syd, melb & hob))',
'bsb50820  diploma of project management',
'bsb50820 diploma of project management',
'bsb60120 advanced diploma of business',
'bsb60420 advance diploma of leadership and management',
'bsb60420 advanced diploma of leadership and management',
'bsb60720 advanced diploma of program management',
'bsb80120 graduate diploma of management (learning)',
'bsb80220 graduate diploma of portfolio management',
'bsb80320 graduate diploma of strategic leadership',
'certificate iii in air conditioning and refrigeration',
'certificate iii in automotive electrical technology',
'certificate iii in commercial cookery',
'certificate iii in early childhood education and care',
'certificate iii in individual support (ageing and disability)',
'certificate iii in individual support (ageing)',
'certificate iii in individual support (disability)',
'certificate iii in light vehicle mechanical technology',
'certificate iii in painting and decorating',
'certificate iii in patisserie',
'certificate iv in accounting and bookkeeping',
'certificate iv in ageing support',
'certificate iv in air conditioning and refrigeration servicing',
'certificate iv in automotive electrical technology',
'certificate iv in automotive mechanical diagnosis',
'certificate iv in disability support',
'certificate iv in information technology',
'certificate iv in kitchen management',
'certificate iv in patisserie',
'certificate iv in project management practice',
'chc30121 certificate iii in early childhood education and care',
'chc33021 certificate iii in individual support - (ageing) (syd, melb, hob)',
'chc33021 certificate iii in individual support (ageing) 52 weeks',
'chc33021 certificate iii in individual support (disability)  cluster',
'chc43015 certificate iv in ageing support(syd, bsb, hob) 26 weeks',
'chc43121 certificate iv in disability support',
'chc50121 diploma of early childhood education and care',
'chc52021 diploma of community services',
'chc62015 advanced diploma of community sector management',
'diploma of accounting',
'diploma of automotive management',
'diploma of aviation (aviation management)',
'diploma of civil construction design',
'diploma of community services',
'diploma of early childhood education and care',
'diploma of hospitality management',
'diploma of information technology (telecommunications network engineering)',
'diploma of leadership and management',
'diploma of logistics',
'diploma of project management',
'fns40222 certificate iv in accounting and bookkeeping',
'fns50222 diploma of accounting',
'fns60222 advanced diploma of accounting',
'fns60222 diploma of accounting',
'general english (beginner to upper intermediate)',
'general english (elementary to advanced)',
'graduate diploma of management (learning)',
'graduate diploma of portfolio management',
'graduate diploma of strategic leadership'
];

console.log('🔍 COURSES IN DATABASE BUT NOT IN JSON FILE:');
console.log('==============================================\n');

const missingCourses = dbCourses.filter(course => !jsonCourses.has(course));
missingCourses.sort().forEach((course, index) => {
  console.log(`${index + 1}. ${course}`);
});

console.log(`\nTotal missing courses: ${missingCourses.length}`);

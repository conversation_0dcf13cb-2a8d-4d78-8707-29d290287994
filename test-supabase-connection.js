// Test script to verify Supabase connection
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://djyzepqwkquomocdgnpd.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqeXplcHF3a3F1b21vY2RnbnBkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAzMDE5NzMsImV4cCI6MjA2NTg3Nzk3M30.uvxsYk9vFB5eiRpnPBCrj63YmQfK9SZD_CpRvvlmxcs';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testConnection() {
  try {
    console.log('Testing Supabase connection...');

    // Test basic connection
    const { data, error } = await supabase.from('courses').select('count').limit(1);

    if (error) {
      console.error('Connection error:', error);
    } else {
      console.log('Connection successful!');
    }

    // Test auth
    console.log('Testing auth...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'SCVTeam2025!'
    });

    if (authError) {
      console.error('Auth error:', authError);
    } else {
      console.log('Auth successful!', authData);
    }

  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

testConnection();

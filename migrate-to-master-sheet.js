// Migration Script: Create Master Google Sheet from existing data
// This script will help migrate your 6 college tabs into a master format

const fs = require('fs');

// College names from your existing tabs
const COLLEGES = ['AIBT', 'REACH', 'NPA', 'AVT', 'AAIBT', 'IBIC'];

// Read JSON file first
console.log('📖 Reading intake-dates.json...');
let intakeData = {};
try {
  const jsonData = fs.readFileSync('intake-dates.json', 'utf8');
  const intakes = JSON.parse(jsonData);
  
  // Convert to easier lookup format
  intakes.forEach(college => {
    intakeData[college.collegeName] = {};
    college.courses.forEach(course => {
      intakeData[college.collegeName][course.courseName] = course.intakes.map(intake => intake.date);
    });
  });
  
  console.log('✅ JSON data loaded successfully');
  console.log(`📊 Found ${Object.keys(intakeData).length} colleges in JSON`);
  
  // Show JSON structure
  Object.keys(intakeData).forEach(college => {
    console.log(`   ${college}: ${Object.keys(intakeData[college]).length} courses`);
  });
  
} catch (error) {
  console.error('❌ Error reading JSON:', error.message);
  process.exit(1);
}

// Since I can't read the large Google Sheets directly with MCP due to size limits,
// I'll create a template for the master sheet structure

console.log('\n🏗️  Creating Master Sheet Structure...');

// Master Courses structure
const masterCoursesHeaders = [
  'college_name',
  'faculty', 
  'course_name',
  'vet_code',
  'cricos_code'
];

// Intake Dates structure - we'll need multiple date columns
const maxIntakeDates = Math.max(...Object.values(intakeData).map(college => 
  Math.max(...Object.values(college).map(dates => dates.length))
));

console.log(`📅 Maximum intake dates found: ${maxIntakeDates}`);

const intakeDatesHeaders = [
  'college_name',
  'course_name',
  ...Array.from({length: maxIntakeDates}, (_, i) => `intake_date_${i + 1}`)
];

console.log('\n📋 Master Courses Headers:', masterCoursesHeaders);
console.log('📋 Intake Dates Headers:', intakeDatesHeaders);

// Create CSV templates
const masterCoursesCsv = masterCoursesHeaders.join(',') + '\n';
const intakeDatesCsv = intakeDatesHeaders.join(',') + '\n';

// Create intake dates CSV from JSON
console.log('\n📝 Creating Intake Dates CSV from JSON...');
let intakeCsvRows = [];

Object.keys(intakeData).forEach(collegeName => {
  const college = intakeData[collegeName];
  Object.keys(college).forEach(courseName => {
    const dates = college[courseName];
    const row = [
      collegeName,
      courseName,
      ...dates,
      ...Array(maxIntakeDates - dates.length).fill('') // Fill empty columns
    ];
    intakeCsvRows.push(row.join(','));
  });
});

const completeIntakeCsv = intakeDatesCsv + intakeCsvRows.join('\n');

// Save templates
fs.writeFileSync('master-courses-template.csv', masterCoursesCsv);
fs.writeFileSync('intake-dates-from-json.csv', completeIntakeCsv);

console.log('\n✅ Created template files:');
console.log('   📄 master-courses-template.csv (empty, ready for your course data)');
console.log('   📄 intake-dates-from-json.csv (populated from JSON)');

console.log('\n📊 Intake Dates Summary:');
console.log(`   Total colleges: ${Object.keys(intakeData).length}`);
console.log(`   Total courses: ${intakeCsvRows.length}`);
console.log(`   Maximum dates per course: ${maxIntakeDates}`);

// Show course name examples for matching
console.log('\n🔍 Sample course names from JSON (for matching):');
Object.keys(intakeData).forEach(college => {
  const courses = Object.keys(intakeData[college]).slice(0, 3);
  console.log(`   ${college}:`);
  courses.forEach(course => {
    console.log(`     - ${course}`);
  });
});

console.log('\n🔄 Next Steps:');
console.log('1. Export each college tab from Google Sheets as CSV');
console.log('2. Combine them into master-courses-template.csv format');
console.log('3. Upload both CSVs to create your Master Google Sheet');
console.log('4. Use the intake-dates-from-json.csv as your intake dates tab');
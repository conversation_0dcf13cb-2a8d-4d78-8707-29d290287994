# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Development
- `npm run dev` - Start development server on http://localhost:3000
- `npm run build` - Build production application
- `npm start` - Start production server
- `npm run lint` - Run ESLint checks

### Testing
- No specific test framework is configured yet - check package.json for any test scripts before running tests

## Project Overview

This is **EduPace**, a comprehensive educational platform built with Next.js 14 that includes a Student Deferment Calculator system and other educational tools. The application replaces complex Excel-based processes for educational institutions, calculating course deferrals and managing intake dates across multiple colleges.

## Architecture

### Technology Stack
- **Next.js 14** with App Router and TypeScript
- **Tailwind CSS** for styling with dark theme
- **date-fns** for date calculations
- **lucide-react** for icons
- **Static JSON data** (`intake-dates.json`) for course information

### Application Structure
```
/
├── intake-dates.json          # Course intake data (project root)
├── app/                       # Next.js App Router
│   ├── layout.tsx            # Root layout with sidebar
│   ├── page.tsx              # Redirects to calculator
│   ├── globals.css           # Tailwind + custom styles
│   ├── deferment-calculator/ # Calculator feature
│   └── intake-dates/         # Intake viewer feature
├── components/               # React components
│   ├── Sidebar.tsx          # Navigation
│   ├── DefermentCalculator.tsx
│   ├── IntakeDatesViewer.tsx
│   └── CourseCard.tsx
└── lib/                     # Utilities and types
    ├── types.ts
    ├── utils/dateUtils.ts
    └── data/courseData.ts
```

## Core Features

### 1. Deferment Calculator
- **Purpose**: Calculate new course dates when students defer studies
- **Input**: Student number, course details, deferment dates
- **Output**: Adjusted dates, deferment duration, sequential course scheduling

### 2. Intake Dates Viewer
- **Purpose**: Display available course intake dates
- **Features**: College filtering, course search, date highlighting

## Critical Business Rules

### Date Calculation Logic
- **Course START dates**: Must be Mondays
- **Course END dates**: Must be Sundays
- **Resumption dates**: Automatically adjusted to next Monday
- **Sequential courses**: Up to 4 courses, each starting on next available intake date

### Data Handling
- **Invalid dates**: Handle "1900-01-00" entries gracefully
- **Course duration**: Extract from course names or use defaults
- **College/Course relationship**: Hierarchical structure in JSON

## Component Architecture

### Server vs Client Components
- **Server Components**: Data fetching (intake dates viewer)
- **Client Components**: Interactive features (calculator with state)
- Use `'use client'` directive for stateful components

### State Management
- **React hooks** for component-level state
- **No global state** management (Redux/Zustand not needed)
- **Controlled components** for form inputs

## Data Structure

### Core Interfaces
```typescript
interface College {
  collegeName: string;
  courses: Course[];
}

interface Course {
  courseName: string;
  intakes: IntakeDate[];
}

interface IntakeDate {
  date: string; // ISO format
}
```

### Key Data Files
- `intake-dates.json`: Contains all college and course intake information
- Static data loaded server-side and passed to client components

## Development Guidelines

### Date Utilities
- Use `date-fns` for all date calculations
- Implement Monday/Sunday adjustment functions
- Handle week-based calculations for deferment duration

### Error Handling
- Graceful handling of invalid dates
- Form validation with clear error messages
- Error boundaries for unexpected failures

### Styling
- Dark theme with Tailwind CSS
- Custom component classes in globals.css
- Responsive design for mobile/tablet

## Important Notes

- The application handles sensitive academic data - ensure proper validation
- All calculations are client-side for performance
- Static JSON data source - no database required
- App Router requires careful Server/Client component distinction

## Data Management Workflow

### New Data Import System (Added 2025-01-18)

The application now supports importing clean course data from Google Sheets to replace corrupted database entries:

#### Google Sheets Import Process
- **Purpose**: Import clean course data with valid CRICOS codes from Google Sheets
- **Supports Multiple Sheets**: Handles different college tabs (AWT, AIBT, NPA, etc.)
- **Data Structure**: Each sheet contains courses with Faculty, Course Name, VET Code, CRICOS Code
- **Safety**: Optionally clears corrupted data (fake CRICOS codes) before import

#### Complete Data Workflow
1. **Master Data**: Google Sheets (multiple tabs for different colleges)
2. **Clean Import**: `/admin/import-sheets` - Import courses with valid CRICOS codes
3. **Schedule Data**: JSON files with intake dates for each course
4. **Schedule Sync**: `/admin/sync` - Sync intake dates (safe after clean import)

#### API Endpoints
- `POST /api/import-sheets` - Import multi-sheet Google Sheets data
- `POST /api/sync-json` - Sync JSON intake dates (existing, now safe)

#### Data Corruption Prevention
- **Fail-safe Import**: Only updates existing courses or creates with valid data
- **CRICOS Validation**: Rejects fake CRICOS codes (starting with 'CR')
- **Multi-sheet Support**: Processes all college tabs in single operation
- **Error Reporting**: Detailed feedback on skipped/problematic courses

## Implementation Notes

When building components:

- Convert HTML resume content to dynamic React components
- Maintain clean, professional styling consistent with existing resume
- Use TypeScript for type safety
- Always validate CRICOS codes before database operations
- Use multi-sheet import for comprehensive data updates

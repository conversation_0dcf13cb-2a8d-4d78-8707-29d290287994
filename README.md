# EduPace - Comprehensive Educational Platform

<div align="center">
  <img src="public/favicon.svg" alt="EduPace Logo" width="80" height="80">

**A modern, comprehensive educational platform with tools for course management, deferment calculations, intake dates, and academic planning.**

[![Next.js](https://img.shields.io/badge/Next.js-14.2.19-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.1-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)
[![Supabase](https://img.shields.io/badge/Supabase-2.51.0-3ECF8E?style=flat-square&logo=supabase)](https://supabase.com/)

</div>

## 🌟 Overview

EduPace is a sophisticated educational management platform designed to streamline academic processes for educational institutions. It provides powerful tools for managing student deferments, calculating payment plans, tracking intake dates, and handling course administration with an intuitive, modern interface.

## ✨ Key Features

### 🎓 Student Deferment Calculator

- **Multi-Course Support**: Handle up to 4 courses with automatic scheduling
- **Intelligent Date Calculations**: Automatic start/end date calculations with Monday-Sunday week alignment
- **Payment Integration**: Comprehensive fee allocation and payment plan calculations
- **Auto-fill Logic**: Smart auto-population of subsequent course dates
- **Export Capabilities**: Download detailed reports in text and CSV formats
- **Real-time Validation**: Instant feedback and error checking

### 💰 Fee Calculator

- **Dynamic Fee Calculations**: Real-time tuition, material, and placement fee calculations
- **Payment Plan Generation**: Automated payment schedule creation
- **Fee Allocation**: Smart distribution of fees across course duration
- **Remaining Balance Tracking**: Monitor outstanding payments and unspent fees

### 📅 Intake Dates Management

- **Course Search**: Advanced filtering and search capabilities
- **Multi-Provider Support**: Handle multiple educational providers
- **Location-based Filtering**: Filter courses by campus locations
- **Real-time Updates**: Live data synchronization with course databases

### 🔐 Admin Dashboard

- **Secure Authentication**: Role-based access control
- **Calculation Management**: View, edit, and manage saved calculations
- **Data Export**: Bulk export capabilities for administrative reporting
- **User Management**: Comprehensive user administration tools

## 🛠️ Technology Stack

### Frontend

- **Framework**: Next.js 14.2.19 with App Router
- **Language**: TypeScript 5.0
- **Styling**: Tailwind CSS 3.4.1 with custom design system
- **UI Components**: Radix UI primitives with custom styling
- **Icons**: Lucide React for consistent iconography
- **Date Handling**: date-fns for robust date manipulation

### Backend & Database

- **Database**: Supabase (PostgreSQL) with real-time capabilities
- **Authentication**: Supabase Auth with JWT tokens
- **API**: Next.js API routes with TypeScript
- **File Storage**: Supabase Storage for document management

### Development & Testing

- **Testing**: Jest with React Testing Library
- **Linting**: ESLint with Next.js configuration
- **Type Checking**: TypeScript with strict mode
- **Package Manager**: npm with lock file for consistency

### Design & UX

- **Design System**: Custom Tailwind configuration with brand colors
- **Responsive Design**: Mobile-first approach with breakpoint optimization
- **Animations**: Custom CSS animations and transitions
- **Accessibility**: WCAG 2.1 compliant with screen reader support

## 🚀 Getting Started

### Prerequisites

- Node.js 18.0 or higher
- npm or yarn package manager
- Supabase account and project

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd edupace
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Environment Setup**
   Create a `.env.local` file in the root directory:

   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   ```

4. **Database Setup**

   - Set up your Supabase project
   - Run the provided SQL migrations
   - Configure Row Level Security (RLS) policies

5. **Start Development Server**

   ```bash
   npm run dev
   ```

6. **Open Application**
   Navigate to `http://localhost:3000`

## 📁 Project Structure

```
edupace/
├── app/                          # Next.js App Router pages
│   ├── admin/                    # Admin dashboard pages
│   ├── api/                      # API routes
│   ├── deferment-calculator/     # Deferment calculator page
│   ├── fee-calculator/           # Fee calculator page
│   ├── intake-dates/             # Intake dates viewer
│   └── login/                    # Authentication pages
├── components/                   # Reusable React components
│   ├── ui/                       # Base UI components
│   ├── DefermentCalculatorEnhanced.tsx
│   ├── PaymentPlanCalculator.tsx
│   └── IntakeDatesViewer.tsx
├── lib/                          # Utility libraries
│   ├── supabase/                 # Supabase client and services
│   ├── utils/                    # Helper functions
│   └── types.ts                  # TypeScript type definitions
├── contexts/                     # React contexts
├── public/                       # Static assets
└── scripts/                      # Build and deployment scripts
```

## 🔧 Configuration

### Tailwind CSS

The project uses a comprehensive Tailwind configuration with:

- Custom color palette for brand consistency
- Extended spacing and typography scales
- Custom animations and keyframes
- Glass-morphism effects and modern shadows

### Supabase Integration

- Real-time database subscriptions
- Row Level Security for data protection
- File storage for document management
- Authentication with social providers

## 📊 Features Deep Dive

### 🎯 Deferment Calculator

The core feature of EduPace, providing comprehensive student deferment management:

**Core Functionality:**

- **Multi-Course Management**: Support for up to 4 sequential courses
- **Intelligent Date Calculations**: Automatic Monday-to-Sunday week alignment
- **Deferment Duration Tracking**: Precise calculation of deferment periods
- **Auto-fill Logic**: Smart population of subsequent course start dates
- **Remaining Study Duration**: Real-time calculation of remaining course time

**Payment Integration:**

- **Fee Allocation Calculator**: Distributes fees across course duration
- **Payment Plan Generation**: Creates detailed payment schedules
- **Unspent Fee Tracking**: Monitors and calculates unused tuition fees
- **Formula Transparency**: Shows calculation formulas with collapsible details

**Export & Reporting:**

- **Text Reports**: Comprehensive formatted reports (.txt)
- **CSV Export**: Structured data for spreadsheet analysis
- **Real-time Preview**: Live calculation updates
- **Copy to Clipboard**: Quick data sharing capabilities

### 🏛️ Admin Dashboard

Comprehensive administrative interface for managing the platform:

**Calculation Management:**

- **View All Calculations**: Paginated list with search and filtering
- **Edit Capabilities**: Modify calculation titles and notes
- **Delete Operations**: Secure deletion with confirmation
- **Bulk Export**: CSV export of all calculations

**Download Options:**

- **Individual Reports**: Download specific calculation reports
- **Multiple Formats**: Text and CSV export options
- **Batch Operations**: Bulk download capabilities
- **File Naming**: Automatic naming with timestamps

**User Interface:**

- **Responsive Design**: Works on all device sizes
- **Search & Filter**: Advanced filtering by payment calculation status
- **Statistics Dashboard**: Overview cards with key metrics
- **Real-time Updates**: Live data synchronization

### 💳 Fee Calculator

Specialized tool for educational fee management:

**Fee Types:**

- **Tuition Fees**: Primary course fees with payment tracking
- **Material Fees**: Course materials and resources
- **Placement Fees**: Job placement and career services
- **Other Fees**: Miscellaneous educational expenses

**Calculation Features:**

- **Dynamic Updates**: Real-time fee calculations
- **Payment Schedules**: Automated payment plan generation
- **Balance Tracking**: Monitor outstanding amounts
- **Fee Allocation**: Smart distribution across course periods

### 📅 Intake Dates System

Comprehensive course and intake management:

**Course Database:**

- **Multi-Provider Support**: Handle multiple educational institutions
- **Location Filtering**: Filter by campus and location
- **Course Search**: Advanced search with multiple criteria
- **Real-time Data**: Live synchronization with course databases

**Data Management:**

- **Import/Export**: Bulk data operations
- **Validation**: Data integrity checks
- **Synchronization**: Automated data updates
- **Backup Systems**: Regular data backups

## 🗄️ Database Schema

### Core Tables

**deferment_calculations**

- Stores main calculation metadata
- Links to multiple courses
- Tracks payment calculation settings
- Maintains creation and update timestamps

**deferment_courses**

- Individual course data within calculations
- Date tracking (original, deferment, new schedules)
- Duration calculations (weeks, days, months)
- Payment and fee information

**colleges & courses**

- Educational institution data
- Course catalog with CRICOS codes
- Location and campus information
- Intake date management

**intake_dates**

- Course-specific intake schedules
- Date validation and formatting
- Provider-specific intake patterns

### API Endpoints

**Deferment Management**

- `POST /api/deferment/save` - Save calculation to database
- `GET /api/deferment/list` - Retrieve saved calculations
- `PUT /api/deferment/update` - Update existing calculations
- `DELETE /api/deferment/delete` - Remove calculations

**Course Data**

- `GET /api/courses` - Fetch course catalog
- `GET /api/intake-dates` - Get intake schedules
- `POST /api/import-sheets` - Bulk import course data
- `GET /api/search` - Advanced course search

**Authentication**

- `POST /api/auth/login` - User authentication
- `POST /api/auth/logout` - Session termination
- `GET /api/auth/user` - Current user information

## 🧪 Testing

Run the test suite:

```bash
npm test                # Run tests once
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Generate coverage report
```

### Test Coverage

- **Components**: React component testing with React Testing Library
- **Utilities**: Unit tests for calculation functions
- **API Routes**: Integration tests for backend endpoints
- **E2E Testing**: End-to-end user workflow testing

## 🔒 Security Features

### Authentication & Authorization

- **JWT Tokens**: Secure session management
- **Row Level Security**: Database-level access control
- **Role-based Access**: Admin and user role separation
- **Session Management**: Automatic token refresh and expiration

### Data Protection

- **Input Validation**: Comprehensive form validation
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Content sanitization
- **CSRF Protection**: Cross-site request forgery prevention

### Privacy & Compliance

- **Data Encryption**: Sensitive data encryption at rest
- **Audit Logging**: User action tracking
- **Data Retention**: Configurable data retention policies
- **GDPR Compliance**: Privacy regulation adherence

## 🚀 Deployment

### Production Build

```bash
npm run build
npm start
```

### Environment Variables

Ensure all required environment variables are set in your production environment.

### Deployment Platforms

- **Vercel**: Optimized for Next.js applications
- **Netlify**: Static site deployment with serverless functions
- **AWS**: Full-stack deployment with EC2/Lambda
- **Docker**: Containerized deployment for any platform

## ⚡ Performance & Optimization

### Frontend Optimization

- **Code Splitting**: Automatic route-based code splitting
- **Image Optimization**: Next.js Image component with lazy loading
- **Bundle Analysis**: Webpack bundle analyzer integration
- **Caching Strategy**: Aggressive caching for static assets

### Database Optimization

- **Query Optimization**: Indexed queries for fast data retrieval
- **Connection Pooling**: Efficient database connection management
- **Real-time Subscriptions**: Optimized Supabase real-time features
- **Data Pagination**: Efficient large dataset handling

### Monitoring & Analytics

- **Performance Metrics**: Core Web Vitals tracking
- **Error Monitoring**: Comprehensive error tracking and reporting
- **User Analytics**: Usage patterns and feature adoption
- **Database Monitoring**: Query performance and optimization

## 🔧 Development Tools

### Code Quality

- **ESLint**: Comprehensive linting rules
- **Prettier**: Consistent code formatting
- **TypeScript**: Static type checking
- **Husky**: Git hooks for quality gates

### Development Experience

- **Hot Reload**: Instant development feedback
- **TypeScript IntelliSense**: Enhanced IDE support
- **Component Storybook**: Component development and testing
- **API Documentation**: Automated API documentation generation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For support and questions:

- Create an issue in the repository
- Contact the development team
- Check the documentation in the `/docs` folder

---

<div align="center">
  <strong>Built with ❤️ for educational excellence</strong>
</div>

# EduPace CT Calculator Setup Guide

## Overview

The CT Calculator is now integrated into EduPace! This guide will help you configure the AI provider and start using the Credit Transfer application assistant.

## ✅ What's Already Installed

The CT Calculator integration includes:

- **Core calculation logic** with unit-based formulas
- **Universal AI provider** supporting multiple services
- **Chat interface** with professional UI matching EduPace design
- **Navigation integration** in the sidebar
- **API endpoints** for chat functionality
- **Results display** with compliance checklists and instructions

## 🔧 Environment Configuration

### 1. AI Provider Setup

The CT Calculator requires an AI provider API key. We recommend starting with **Groq** (free tier available):

#### Option A: Groq (Recommended - Free Tier)
1. Visit [https://console.groq.com](https://console.groq.com)
2. Sign up for a free account
3. Generate an API key
4. Update `.env.local`:
```env
AI_PROVIDER=groq
AI_BASE_URL=https://api.groq.com/openai/v1
AI_MODEL_NAME=llama3-70b-8192
AI_API_KEY=your_actual_groq_api_key_here
```

#### Option B: OpenAI
```env
AI_PROVIDER=openai
AI_BASE_URL=https://api.openai.com/v1
AI_MODEL_NAME=gpt-4-turbo-preview
AI_API_KEY=your_openai_api_key_here
```

#### Option C: Together AI
```env
AI_PROVIDER=together
AI_BASE_URL=https://api.together.xyz/v1
AI_MODEL_NAME=meta-llama/Llama-2-70b-chat-hf
AI_API_KEY=your_together_api_key_here
```

### 2. Verify Configuration

After setting up your API key, test the connection:

```bash
# Start the development server
npm run dev

# Visit the health check endpoint
curl http://localhost:3000/api/chat/ct-advisor
```

You should see a response like:
```json
{
  "status": "healthy",
  "aiProvider": {
    "connected": true,
    "provider": "groq",
    "model": "llama3-70b-8192"
  }
}
```

## 🚀 Using the CT Calculator

### 1. Access the Calculator

Navigate to **CT Calculator** in the EduPace sidebar, or visit:
```
http://localhost:3000/ct-calculator
```

### 2. Three CT Workflows Supported

#### **First Aid CT**
- **Identifier**: First Aid units (HLTAID011, etc.)
- **Duration**: Never changes
- **Calculation**: Fee reduction = Invoice amount
- **Example**: "Student has HLTAID011, original fee $15,000, invoice $500"

#### **General CT**
- **Identifier**: Standard units within current course
- **Duration**: Course ends earlier
- **Calculation**: New Fee = (Original Fee ÷ Total Units) × Units After CT
- **Example**: "20 units total, 4 credited, original fee $15,000"

#### **Change of Qualification (COQ)**
- **Identifier**: Adding new course to study plan
- **Duration**: Entire plan restructured
- **Calculation**: Same formula applied to each course
- **Example**: "Student changing from Cert III to Cert IV, 15 units credited"

### 3. How to Use

1. **Start a conversation** with CT Advisor
2. **Share student information**:
   - Student name and course details
   - Original fee amount
   - Unit information (can paste unit tables)
   - Invoice amounts (for First Aid CT)

3. **Get AI guidance**:
   - Workflow identification
   - Step-by-step calculations
   - Compliance checklists
   - Portal and PRISMS instructions

4. **Export results**:
   - Download calculation reports
   - Save to admin dashboard
   - Copy instructions for Portal/PRISMS

## 📋 Example Interactions

### First Aid CT Example
```
User: "Student John Smith has HLTAID011 First Aid unit. Original course fee is $15,000. Invoice shows $500 for the First Aid training."

CT Advisor Response:
🔍 Workflow Identified: First Aid CT
📊 Calculation:
Original Fee: $15,000
Invoice Amount: $500
Formula: Fee reduction = Invoice amount ($500)
💰 New Fee: $14,500 (Savings: $500)
Note: Course duration unchanged for First Aid CT
```

### General CT Example
```
User: "Student has completed 4 units out of 20 total units. Original fee was $15,000."

CT Advisor Response:
🔍 Workflow Identified: General CT
📊 Calculation:
Original Fee: $15,000 for 20 units
Credited Units: 4 units
Formula: ($15,000 ÷ 20) × 16 = $12,000
💰 New Fee: $12,000 (Savings: $3,000)
```

## 🔍 Troubleshooting

### Common Issues

1. **"AI Provider Error"**
   - Check your API key is correct
   - Verify the provider service is accessible
   - Try switching to a different provider

2. **"Failed to process request"**
   - Check your internet connection
   - Verify environment variables are set
   - Check the browser console for detailed errors

3. **Calculations not appearing**
   - Ensure you provide fee amounts and unit information
   - Use clear language about the CT type
   - Include specific numbers in your messages

### Debug Steps

1. **Check environment variables**:
```bash
echo $AI_PROVIDER
echo $AI_API_KEY
```

2. **Test API endpoint directly**:
```bash
curl -X POST http://localhost:3000/api/chat/ct-advisor \
  -H "Content-Type: application/json" \
  -d '{"messages":[{"role":"user","content":"Hello"}]}'
```

3. **Check browser console** for JavaScript errors

## 🎯 Features

### ✅ Implemented
- ✅ Three CT workflow support (First Aid, General, COQ)
- ✅ Unit-based calculations with correct formulas
- ✅ AI-powered chat interface
- ✅ Compliance checklists generation
- ✅ Portal and PRISMS instructions
- ✅ Export functionality
- ✅ Professional UI matching EduPace design
- ✅ Multi-provider AI support

### 🚧 Coming Soon
- 🚧 Save calculations to admin dashboard
- 🚧 Integration with existing student records
- 🚧 Bulk processing capabilities
- 🚧 Advanced reporting features

## 📞 Support

If you encounter issues:

1. Check this setup guide first
2. Verify your environment configuration
3. Test with simple examples
4. Check the browser console for errors

The CT Calculator is designed to handle complex CT applications with AI guidance, making the process faster and more accurate than manual calculations.

## 🔄 Updates

This integration follows EduPace patterns and can be easily extended with additional features as needed. The modular design allows for easy maintenance and updates.

---

**Ready to start?** Configure your AI provider API key and visit `/ct-calculator` to begin!

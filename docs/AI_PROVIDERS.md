# AI Provider Configuration Guide

This application supports multiple AI providers for the CT Calculator. Configure your preferred provider using environment variables.

## Supported Providers

### 1. OpenAI (Default)
```env
AI_PROVIDER=openai
AI_BASE_URL=https://api.openai.com/v1
AI_MODEL_NAME=gpt-4o-mini
AI_API_KEY=your_openai_api_key_here
```

**Available Models:**
- `gpt-4o-mini` (recommended for cost efficiency)
- `gpt-4o`
- `gpt-4-turbo`
- `gpt-3.5-turbo`

### 2. Groq (Fast & Free Tier)
```env
AI_PROVIDER=groq
AI_BASE_URL=https://api.groq.com/openai/v1
AI_MODEL_NAME=llama3-70b-8192
AI_API_KEY=your_groq_api_key_here
```

**Available Models:**
- `llama3-70b-8192` (recommended)
- `llama3-8b-8192`
- `mixtral-8x7b-32768`
- `gemma-7b-it`

### 3. Moonshot AI 🌙
```env
AI_PROVIDER=moonshot
AI_BASE_URL=https://api.moonshot.cn/v1
AI_MODEL_NAME=moonshot-v1-8k
AI_API_KEY=your_moonshot_api_key_here
```

**Available Models:**
- `moonshot-v1-8k` (8K context window)
- `moonshot-v1-32k` (32K context window)
- `moonshot-v1-128k` (128K context window)

**Features:**
- Chinese language optimized
- Competitive pricing
- OpenAI-compatible API
- High performance for educational content

### 4. Together AI
```env
AI_PROVIDER=together
AI_BASE_URL=https://api.together.xyz/v1
AI_MODEL_NAME=meta-llama/Llama-2-70b-chat-hf
AI_API_KEY=your_together_api_key_here
```

**Available Models:**
- `meta-llama/Llama-2-70b-chat-hf`
- `meta-llama/Llama-2-13b-chat-hf`
- `mistralai/Mixtral-8x7B-Instruct-v0.1`

### 5. Anthropic Claude
```env
AI_PROVIDER=anthropic
AI_BASE_URL=https://api.anthropic.com
AI_MODEL_NAME=claude-3-sonnet-********
AI_API_KEY=your_anthropic_api_key_here
```

**Available Models:**
- `claude-3-sonnet-********`
- `claude-3-haiku-********`
- `claude-3-opus-********`

## Getting API Keys

### Moonshot AI
1. Visit [https://platform.moonshot.ai/](https://platform.moonshot.ai/)
2. Sign up for an account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key to your `.env.local` file

### Other Providers
- **OpenAI**: [https://platform.openai.com/api-keys](https://platform.openai.com/api-keys)
- **Groq**: [https://console.groq.com/keys](https://console.groq.com/keys)
- **Together AI**: [https://api.together.xyz/settings/api-keys](https://api.together.xyz/settings/api-keys)
- **Anthropic**: [https://console.anthropic.com/](https://console.anthropic.com/)

## Configuration Tips

### For Development
- Use **Groq** for fast, free testing
- Use **OpenAI gpt-4o-mini** for balanced cost/quality

### For Production
- Use **OpenAI gpt-4o** for best quality
- Use **Moonshot AI** for Chinese language support
- Use **Claude** for complex reasoning tasks

### Cost Optimization
1. **Groq**: Free tier available
2. **OpenAI gpt-4o-mini**: Most cost-effective OpenAI model
3. **Moonshot AI**: Competitive pricing for Chinese market
4. **Together AI**: Good value for open-source models

## Environment Variables

Add these to your `.env.local` file:

```env
# Choose your provider
AI_PROVIDER=moonshot
AI_BASE_URL=https://api.moonshot.cn/v1
AI_MODEL_NAME=moonshot-v1-8k
AI_API_KEY=your_moonshot_api_key_here
```

## Vercel Deployment

For Vercel deployment, add these environment variables in your Vercel dashboard:

1. Go to your Vercel project settings
2. Navigate to "Environment Variables"
3. Add the following variables:
   - `AI_PROVIDER`
   - `AI_BASE_URL`
   - `AI_MODEL_NAME`
   - `AI_API_KEY`

## Testing Your Configuration

1. Start the development server: `npm run dev`
2. Navigate to the CT Calculator
3. Send a test message to verify the AI responds correctly
4. Check the browser console for any API errors

## Troubleshooting

### Common Issues
1. **Invalid API Key**: Double-check your API key is correct
2. **Rate Limits**: Some providers have rate limits on free tiers
3. **Model Not Found**: Verify the model name is correct for your provider
4. **Network Issues**: Check if your region can access the provider's API

### Error Messages
- `AI API key is required`: Set the `AI_API_KEY` environment variable
- `HTTP 401`: Invalid API key
- `HTTP 429`: Rate limit exceeded
- `HTTP 404`: Invalid model name or endpoint

## Support

For provider-specific issues:
- Check the provider's documentation
- Verify your account has sufficient credits
- Test with their official examples first

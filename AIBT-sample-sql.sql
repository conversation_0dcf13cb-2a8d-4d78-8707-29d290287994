-- SQL for AIBT College - Sample from first 10 rows
-- Generated from Google Sheets data

-- Clear existing AIBT data (optional)
DELETE FROM courses WHERE college_name = 'AIBT';

-- Insert AIBT courses
INSERT INTO courses (college_name, faculty, course_name, vet_code, cricos_code, created_at, updated_at) VALUES
('AIBT', 'ACE AVIATION AEROSPACE ACADEMY', 'Diploma of Aviation (Aviation Management)', 'AVI50119', '101215', NOW(), NOW()),
('AIBT', 'BESPOKE GRAMMAR SCHOOL OF ENGLISH', 'General English (Beginner to Upper Intermediate)', NULL, '089486D', NOW(), NOW()),
('AIBT', 'BRANSON SCHOOL OF BUSINESS', 'Certificate IV in Accounting and Bookkeeping', 'FNS40222', '111047F', NOW(), NOW()),
('AIBT', 'BRANSON SCHOOL OF BUSINESS', 'Diploma of Accounting', 'FNS50222', '113810D', NOW(), NOW()),
('AIBT', 'BRANSON SCHOOL OF BUSINESS', 'Advanced Diploma of Accounting', 'FNS60222', '113811C', NOW(), NOW()),
('AIBT', 'BRANSON SCHOOL OF BUSINESS', 'Certificate IV in Project Management Practice', 'BSB40920', '103930A', NOW(), NOW()),
('AIBT', 'BRANSON SCHOOL OF BUSINESS', 'Diploma of Project Management', 'BSB50820', '104063J', NOW(), NOW()),
('AIBT', 'BRANSON SCHOOL OF BUSINESS', 'Diploma of Leadership and Management', 'BSB50420', '104262B', NOW(), NOW()),
('AIBT', 'BRANSON SCHOOL OF BUSINESS', 'Advanced Diploma of Program Management', 'BSB60720', '104452G', NOW(), NOW());

-- Verify the import
SELECT COUNT(*) as aibt_courses_imported FROM courses WHERE college_name = 'AIBT';
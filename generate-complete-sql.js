// Generate Complete SQL from Google Sheets Data
// This creates the SQL statements for all your college sheets

const fs = require('fs');

// All your college sheets
const COLLEGE_SHEETS = ['AIBT', 'REACH', 'NPA', 'AVT', 'AAIBT', 'IBIC'];

function isValidCRICOSCode(code) {
  if (!code || code === 'N/A' || code.startsWith('CR')) {
    return false;
  }
  
  // CRICOS codes can be 6 digits, or 6 characters with letter at the end
  const cricosPattern = /^(\d{6}|\d{5}[A-Z])$/;
  return cricosPattern.test(code);
}

function escapeSqlString(str) {
  if (!str) return '';
  return str.replace(/'/g, "''");
}

function generateCompleteSQL() {
  let completeSQL = `-- Complete SQL for updating courses database from Google Sheets
-- Generated on ${new Date().toISOString()}
-- 
-- INSTRUCTIONS:
-- 1. Run this SQL in your Supabase database
-- 2. This will clear existing data and insert clean data
-- 3. Then use your existing /admin/sync for intake dates

-- Clear all existing course data (optional - remove this if you want to keep existing data)
DELETE FROM courses;

-- Reset the sequence (optional)
ALTER SEQUENCE courses_id_seq RESTART WITH 1;

`;

  // Since I can't read the full sheets due to size limits, I'll create a template
  // that shows the structure and you can extend it
  
  for (const collegeName of COLLEGE_SHEETS) {
    completeSQL += `
-- ==============================
-- ${collegeName} College Courses
-- ==============================

-- Sample data structure for ${collegeName}
-- INSERT INTO courses (college_name, faculty, course_name, vet_code, cricos_code, created_at, updated_at) VALUES
-- ('${collegeName}', 'FACULTY_NAME', 'COURSE_NAME', 'VET_CODE', 'CRICOS_CODE', NOW(), NOW());

-- TODO: Add actual data for ${collegeName} here
-- You can paste the data I'll generate for each sheet individually

`;
  }

  completeSQL += `
-- ==============================
-- Verification Queries
-- ==============================

-- Check total courses imported
SELECT college_name, COUNT(*) as course_count 
FROM courses 
GROUP BY college_name 
ORDER BY college_name;

-- Check for any invalid CRICOS codes
SELECT college_name, course_name, cricos_code 
FROM courses 
WHERE cricos_code IS NULL OR cricos_code = '' OR cricos_code LIKE 'CR%';

-- Check total count
SELECT COUNT(*) as total_courses FROM courses;
`;

  return completeSQL;
}

// Generate the complete SQL
const sql = generateCompleteSQL();

// Save to file
fs.writeFileSync('complete-database-update.sql', sql);

console.log('✅ Complete SQL template generated!');
console.log('📁 File saved: complete-database-update.sql');
console.log('');
console.log('🔄 Next steps:');
console.log('1. I will read each sheet individually and generate the INSERT statements');
console.log('2. You can copy each section into the template');
console.log('3. Execute the final SQL in your database');
console.log('');
console.log('📊 Colleges to process:', COLLEGE_SHEETS.join(', '));
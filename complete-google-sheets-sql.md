# Google Sheets to Database Solution

## What I Can See From Your Google Sheets

**Spreadsheet ID:** `10_aDbphk8Hn_XsJF2o_BbKEb4u48uJCPKNXSJag3Z_k`

**Available Sheets:** AIBT, REACH, NPA, AVT, AAIBT, IBIC

**Data Structure:**
- Column A: Faculty
- Column B: Course Name
- Column C: VET Code
- Column D: CRICOS Code

## Sample Data from AIBT Sheet

From the first few rows I can read:

```
1. ACE AVIATION AEROSPACE ACADEMY | Diploma of Aviation (Aviation Management) | AVI50119 | 101215
2. BESPOKE GRAMMAR SCHOOL OF ENGLISH | General English (Beginner to Upper Intermediate) | N/A | 089486D
3. BRANSON SCHOOL OF BUSINESS | Certificate IV in Accounting and Bookkeeping | FNS40222 | 111047F
4. BRANSON SCHOOL OF BUSINESS | Diploma of Accounting | FNS50222 | 113810D
```

## The Solution

**Simple 3-Step Process:**

1. **I (<PERSON>) generate SQL statements** from your Google Sheets data
2. **You copy and paste the SQL** into your database management tool
3. **Your database gets updated** with clean data

## Generated SQL for AIBT (Sample)

```sql
-- Clear existing AIBT data (optional)
DELETE FROM courses WHERE college_name = 'AIBT';

-- Insert clean AIBT courses
INSERT INTO courses (college_name, faculty, course_name, vet_code, cricos_code, created_at, updated_at) VALUES
('AIBT', 'ACE AVIATION AEROSPACE ACADEMY', 'Diploma of Aviation (Aviation Management)', 'AVI50119', '101215', NOW(), NOW()),
('AIBT', 'BESPOKE GRAMMAR SCHOOL OF ENGLISH', 'General English (Beginner to Upper Intermediate)', NULL, '089486D', NOW(), NOW()),
('AIBT', 'BRANSON SCHOOL OF BUSINESS', 'Certificate IV in Accounting and Bookkeeping', 'FNS40222', '111047F', NOW(), NOW()),
('AIBT', 'BRANSON SCHOOL OF BUSINESS', 'Diploma of Accounting', 'FNS50222', '113810D', NOW(), NOW());
```

## Next Steps

1. **Tell me to generate the complete SQL** for all your sheets
2. **I'll read all your Google Sheets data** and create the full SQL script
3. **You execute the SQL** in your database
4. **Use your existing /admin/sync** for intake dates

## Why This Works

- ✅ **No production dependencies** - Just SQL statements
- ✅ **Uses your existing database** - No new infrastructure
- ✅ **Clean CRICOS codes** - Filters out fake/invalid codes
- ✅ **Safe operation** - You control what gets executed
- ✅ **Works with your existing sync** - Intake dates sync will work afterward

**Ready to generate the complete SQL for all your sheets?**
-- Create CT Calculations table for storing Credit Transfer calculations
-- Run this SQL in your Supabase database

CREATE TABLE IF NOT EXISTS ct_calculations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Calculation details
  workflow_type VARCHAR(50) NOT NULL,
  student_name VARCHAR(255),
  course_name VARCHAR(255),
  original_fee DECIMAL(10,2) NOT NULL,
  new_fee DECIMAL(10,2) NOT NULL,
  savings DECIMAL(10,2) NOT NULL,
  formula TEXT NOT NULL,
  units_reduction INTEGER DEFAULT 0,
  
  -- Additional data
  notes TEXT,
  calculation_data JSONB, -- Store full calculation object
  
  -- Constraints
  CONSTRAINT ct_calculations_positive_fees CHECK (original_fee >= 0 AND new_fee >= 0),
  CONSTRAINT ct_calculations_valid_savings CHECK (savings >= 0)
);

-- <PERSON>reate indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ct_calculations_user_id ON ct_calculations(user_id);
CREATE INDEX IF NOT EXISTS idx_ct_calculations_created_at ON ct_calculations(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ct_calculations_workflow_type ON ct_calculations(workflow_type);
CREATE INDEX IF NOT EXISTS idx_ct_calculations_student_name ON ct_calculations(student_name);

-- Enable Row Level Security (RLS)
ALTER TABLE ct_calculations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only see their own calculations
CREATE POLICY "Users can view own ct_calculations" ON ct_calculations
  FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own calculations
CREATE POLICY "Users can insert own ct_calculations" ON ct_calculations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own calculations
CREATE POLICY "Users can update own ct_calculations" ON ct_calculations
  FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own calculations
CREATE POLICY "Users can delete own ct_calculations" ON ct_calculations
  FOR DELETE USING (auth.uid() = user_id);

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_ct_calculations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER trigger_update_ct_calculations_updated_at
  BEFORE UPDATE ON ct_calculations
  FOR EACH ROW
  EXECUTE FUNCTION update_ct_calculations_updated_at();

-- Grant necessary permissions
GRANT ALL ON ct_calculations TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;
